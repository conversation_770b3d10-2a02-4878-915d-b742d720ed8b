# CRMEB Go项目构建脚本

# 项目信息
PROJECT_NAME := crmeb-go
VERSION := v1.0.0
BUILD_TIME := $(shell date +%Y-%m-%d\ %H:%M:%S)
GIT_COMMIT := $(shell git rev-parse --short HEAD)
GO_VERSION := $(shell go version | awk '{print $$3}')

# 构建参数
LDFLAGS := -ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT)"
BUILD_DIR := ./build
BINARY_NAME := crmeb-go

# Go相关变量
GOCMD := go
GOBUILD := $(GOCMD) build
GOCLEAN := $(GOCMD) clean
GOTEST := $(GOCMD) test
GOGET := $(GOCMD) get
GOMOD := $(GOCMD) mod
GOFMT := gofmt
GOLINT := golangci-lint

# 默认目标
.PHONY: all
all: clean deps fmt lint test build

# 帮助信息
.PHONY: help
help:
	@echo "CRMEB Go项目构建脚本"
	@echo ""
	@echo "可用命令:"
	@echo "  build      构建项目"
	@echo "  clean      清理构建文件"
	@echo "  deps       下载依赖"
	@echo "  fmt        格式化代码"
	@echo "  lint       代码检查"
	@echo "  test       运行测试"
	@echo "  test-cover 运行测试并生成覆盖率报告"
	@echo "  run        运行项目"
	@echo "  docker     构建Docker镜像"
	@echo "  swagger    生成Swagger文档"
	@echo "  migrate    运行数据库迁移"
	@echo "  install    安装项目到GOPATH"
	@echo "  release    构建发布版本"
	@echo "  all        执行完整构建流程"

# 下载依赖
.PHONY: deps
deps:
	@echo "下载依赖..."
	$(GOMOD) download
	$(GOMOD) tidy

# 格式化代码
.PHONY: fmt
fmt:
	@echo "格式化代码..."
	$(GOFMT) -s -w .
	$(GOCMD) mod tidy

# 代码检查
.PHONY: lint
lint:
	@echo "代码检查..."
	$(GOLINT) run ./...

# 运行测试
.PHONY: test
test:
	@echo "运行测试..."
	$(GOTEST) -v ./...

# 运行测试并生成覆盖率报告
.PHONY: test-cover
test-cover:
	@echo "运行测试并生成覆盖率报告..."
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "覆盖率报告已生成: coverage.html"

# 构建项目
.PHONY: build
build:
	@echo "构建项目..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) ./cmd/api

# 构建所有服务
.PHONY: build-all
build-all:
	@echo "构建所有服务..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/api ./cmd/api
	$(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/admin ./cmd/admin
	$(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/merchant ./cmd/merchant
	$(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/worker ./cmd/worker

# 清理构建文件
.PHONY: clean
clean:
	@echo "清理构建文件..."
	$(GOCLEAN)
	rm -rf $(BUILD_DIR)
	rm -f coverage.out coverage.html

# 运行项目
.PHONY: run
run:
	@echo "运行项目..."
	$(GOCMD) run ./cmd/api

# 运行开发模式
.PHONY: dev
dev:
	@echo "运行开发模式..."
	air -c .air.toml

# 生成Swagger文档
.PHONY: swagger
swagger:
	@echo "生成Swagger文档..."
	swag init -g cmd/api/main.go -o docs/swagger

# 数据库迁移
.PHONY: migrate
migrate:
	@echo "运行数据库迁移..."
	$(GOCMD) run ./cmd/migrate

# 安装项目
.PHONY: install
install:
	@echo "安装项目..."
	$(GOCMD) install $(LDFLAGS) ./cmd/api

# 构建Docker镜像
.PHONY: docker
docker:
	@echo "构建Docker镜像..."
	docker build -t $(PROJECT_NAME):$(VERSION) .
	docker tag $(PROJECT_NAME):$(VERSION) $(PROJECT_NAME):latest

# 推送Docker镜像
.PHONY: docker-push
docker-push:
	@echo "推送Docker镜像..."
	docker push $(PROJECT_NAME):$(VERSION)
	docker push $(PROJECT_NAME):latest

# 构建发布版本
.PHONY: release
release: clean deps fmt lint test
	@echo "构建发布版本..."
	@mkdir -p $(BUILD_DIR)/release
	
	# Linux amd64
	GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/release/$(BINARY_NAME)-linux-amd64 ./cmd/api
	
	# Linux arm64
	GOOS=linux GOARCH=arm64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/release/$(BINARY_NAME)-linux-arm64 ./cmd/api
	
	# Windows amd64
	GOOS=windows GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/release/$(BINARY_NAME)-windows-amd64.exe ./cmd/api
	
	# macOS amd64
	GOOS=darwin GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/release/$(BINARY_NAME)-darwin-amd64 ./cmd/api
	
	# macOS arm64
	GOOS=darwin GOARCH=arm64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/release/$(BINARY_NAME)-darwin-arm64 ./cmd/api
	
	@echo "发布版本构建完成，文件位于 $(BUILD_DIR)/release/"

# 性能测试
.PHONY: bench
bench:
	@echo "运行性能测试..."
	$(GOTEST) -bench=. -benchmem ./...

# 内存泄漏检测
.PHONY: race
race:
	@echo "运行竞态条件检测..."
	$(GOTEST) -race ./...

# 生成模拟对象
.PHONY: mock
mock:
	@echo "生成模拟对象..."
	mockgen -source=internal/repository/user/user_repository.go -destination=tests/mocks/user_repository_mock.go

# 检查依赖更新
.PHONY: deps-check
deps-check:
	@echo "检查依赖更新..."
	$(GOCMD) list -u -m all

# 更新依赖
.PHONY: deps-update
deps-update:
	@echo "更新依赖..."
	$(GOGET) -u ./...
	$(GOMOD) tidy

# 安全检查
.PHONY: security
security:
	@echo "运行安全检查..."
	gosec ./...

# 代码复杂度检查
.PHONY: complexity
complexity:
	@echo "检查代码复杂度..."
	gocyclo -over 10 .

# 生成版本信息
.PHONY: version
version:
	@echo "项目: $(PROJECT_NAME)"
	@echo "版本: $(VERSION)"
	@echo "构建时间: $(BUILD_TIME)"
	@echo "Git提交: $(GIT_COMMIT)"
	@echo "Go版本: $(GO_VERSION)"

# 启动开发环境
.PHONY: dev-env
dev-env:
	@echo "启动开发环境..."
	docker-compose -f docker/docker-compose.dev.yml up -d

# 停止开发环境
.PHONY: dev-env-down
dev-env-down:
	@echo "停止开发环境..."
	docker-compose -f docker/docker-compose.dev.yml down

# 查看日志
.PHONY: logs
logs:
	@echo "查看应用日志..."
	tail -f logs/app.log

# 健康检查
.PHONY: health
health:
	@echo "健康检查..."
	curl -f http://localhost:8080/health || exit 1
