# CRMEB Go - 多商户电商系统

[![Go Version](https://img.shields.io/badge/Go-1.21+-blue.svg)](https://golang.org)
[![Gin Version](https://img.shields.io/badge/Gin-1.9+-green.svg)](https://github.com/gin-gonic/gin)
[![License](https://img.shields.io/badge/License-Apache%202.0-blue.svg)](https://opensource.org/licenses/Apache-2.0)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)]()

CRMEB Go是基于Golang重构的多商户电商系统，采用现代化的微服务架构设计，具有高性能、高并发、易扩展的特点。

## 🚀 项目特性

### 技术特性
- **高性能**: 基于Gin框架，性能比PHP版本提升3-5倍
- **高并发**: 支持万级并发连接，轻松应对流量高峰
- **微服务架构**: 模块化设计，支持独立部署和扩展
- **容器化部署**: 支持Docker和Kubernetes部署
- **完善监控**: 集成Prometheus、Grafana监控体系

### 业务特性
- **多商户管理**: 支持平台自营和第三方商户入驻
- **商品管理**: 完整的商品管理体系，支持SKU、库存管理
- **订单系统**: 完整的订单流程，支持多种支付方式
- **营销系统**: 优惠券、秒杀、拼团等营销工具
- **用户系统**: 完整的用户体系，支持会员等级、积分系统
- **支付集成**: 支持微信支付、支付宝支付等主流支付方式

## 📋 系统要求

- Go 1.21+
- MySQL 8.0+
- Redis 7.0+
- Docker 20.10+ (可选)
- Kubernetes 1.20+ (可选)

## 🛠️ 快速开始

### 1. 克隆项目
```bash
git clone https://github.com/your-org/crmeb-go.git
cd crmeb-go
```

### 2. 安装依赖
```bash
make deps
```

### 3. 配置环境
```bash
# 复制配置文件
cp configs/config.yaml.example configs/config.yaml

# 编辑配置文件
vim configs/config.yaml
```

### 4. 初始化数据库
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE crmeb_merchant CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 运行数据库迁移
make migrate
```

### 5. 启动服务
```bash
# 开发模式
make dev

# 或者直接运行
make run
```

### 6. 使用Docker启动
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f crmeb-api
```

## 📁 项目结构

```
crmeb-go/
├── cmd/                    # 应用入口
│   ├── api/               # API服务
│   ├── admin/             # 管理后台服务
│   ├── merchant/          # 商户端服务
│   └── worker/            # 队列处理服务
├── internal/              # 内部包
│   ├── api/              # API层
│   ├── service/          # 业务逻辑层
│   ├── repository/       # 数据访问层
│   ├── model/            # 数据模型
│   └── config/           # 配置管理
├── pkg/                   # 公共包
│   ├── auth/             # 认证相关
│   ├── cache/            # 缓存封装
│   ├── database/         # 数据库连接
│   ├── logger/           # 日志系统
│   └── utils/            # 工具函数
├── configs/              # 配置文件
├── docs/                 # 文档
├── docker/               # Docker配置
├── scripts/              # 脚本文件
├── tests/                # 测试文件
└── migrations/           # 数据库迁移
```

## 🔧 开发指南

### 代码规范
```bash
# 格式化代码
make fmt

# 代码检查
make lint

# 运行测试
make test

# 生成覆盖率报告
make test-cover
```

### API文档
启动服务后访问: http://localhost:8080/swagger/index.html

### 生成Swagger文档
```bash
make swagger
```

## 🚀 部署指南

### Docker部署
```bash
# 构建镜像
make docker

# 启动服务
docker-compose up -d
```

### Kubernetes部署
```bash
# 应用配置
kubectl apply -f k8s/

# 查看状态
kubectl get pods -n crmeb
```

### 生产环境部署
```bash
# 构建发布版本
make release

# 部署到服务器
./scripts/deploy.sh
```

## 📊 监控和日志

### 监控面板
- Prometheus: http://localhost:9090
- Grafana: http://localhost:3000 (admin/admin123)

### 日志查看
- Kibana: http://localhost:5601
- 应用日志: `./logs/app.log`

### 健康检查
```bash
# 检查服务健康状态
curl http://localhost:8080/health

# 或使用make命令
make health
```

## 🧪 测试

### 运行测试
```bash
# 单元测试
make test

# 集成测试
make test-integration

# 性能测试
make bench

# 竞态条件检测
make race
```

### 测试覆盖率
```bash
make test-cover
```

## 📈 性能优化

### 性能指标
- QPS: 10,000+
- 响应时间: <100ms
- 内存使用: <1GB
- 并发连接: 1000+

### 优化建议
1. 启用Redis缓存
2. 配置数据库连接池
3. 使用CDN加速静态资源
4. 启用Gzip压缩
5. 配置负载均衡

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 开发规范
- 遵循Go官方代码规范
- 提交前运行测试和代码检查
- 编写清晰的提交信息
- 添加必要的文档和注释

## 📄 许可证

本项目采用 Apache 2.0 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🆘 支持

- 📧 邮箱: <EMAIL>
- 💬 QQ群: 123456789
- 📖 文档: https://docs.crmeb.com
- 🐛 问题反馈: https://github.com/your-org/crmeb-go/issues

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！

## 📊 项目状态

- ✅ 用户认证系统
- ✅ 基础架构搭建
- 🚧 商户管理系统
- 🚧 商品管理系统
- 🚧 订单系统
- 🚧 支付系统
- 🚧 营销系统
- 📋 系统集成测试
- 📋 性能优化
- 📋 文档完善

## 🔄 更新日志

### v1.0.0 (2024-01-01)
- 🎉 初始版本发布
- ✨ 完成基础架构搭建
- ✨ 实现用户认证系统
- ✨ 添加Docker支持
- ✨ 集成监控系统
