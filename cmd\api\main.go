package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	_ "crmeb-go/docs"
	"crmeb-go/internal/api/routes"
	"crmeb-go/internal/config"
	"crmeb-go/internal/container"
	"crmeb-go/pkg/auth"
	"crmeb-go/pkg/cache"
	"crmeb-go/pkg/database"
	"crmeb-go/pkg/logger"

	"github.com/gin-gonic/gin"
)

// @title CRMEB Go API
// @version 1.0
// @description CRMEB多商户系统Go版本API文档
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.crmeb.com/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:8080
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization

func main() {
	// 加载配置
	cfg, err := config.LoadConfig("./configs/config.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}
	config.SetGlobalConfig(cfg)

	// 初始化日志系统
	_, err = logger.InitLogger(&cfg.Log)
	if err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}
	logger.Info("Logger initialized successfully")

	// 初始化数据库
	_, err = database.InitMySQL(&cfg.Database)
	if err != nil {
		logger.Fatalf("Failed to initialize database: %v", err)
	}
	logger.Info("Database initialized successfully")

	// 初始化Redis
	_, err = cache.InitRedis(&cfg.Redis)
	if err != nil {
		logger.Fatalf("Failed to initialize Redis: %v", err)
	}
	logger.Info("Redis initialized successfully")

	// 初始化JWT管理器
	jwtManager := auth.NewJWTManager(&cfg.JWT)
	auth.SetGlobalJWTManager(jwtManager)
	logger.Info("JWT manager initialized successfully")

	// 初始化依赖注入容器
	appContainer := container.NewContainer(cfg)
	container.SetGlobalContainer(appContainer)
	logger.Info("Dependency injection container initialized successfully")

	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 创建Gin引擎
	engine := gin.New()

	// 设置路由
	routes.SetupRoutes(engine, cfg)
	logger.Info("Routes setup completed")

	// 创建HTTP服务器
	server := &http.Server{
		Addr:           fmt.Sprintf(":%d", cfg.Server.Port),
		Handler:        engine,
		ReadTimeout:    cfg.Server.ReadTimeout,
		WriteTimeout:   cfg.Server.WriteTimeout,
		MaxHeaderBytes: cfg.Server.MaxHeaderBytes,
	}

	// 启动服务器
	go func() {
		logger.Infof("Starting server on port %d", cfg.Server.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("Failed to start server: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	logger.Info("Shutting down server...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		logger.Errorf("Server forced to shutdown: %v", err)
	}

	// 关闭数据库连接
	if err := database.Close(); err != nil {
		logger.Errorf("Failed to close database: %v", err)
	}

	// 关闭Redis连接
	if err := cache.Close(); err != nil {
		logger.Errorf("Failed to close Redis: %v", err)
	}

	logger.Info("Server exited")
}
