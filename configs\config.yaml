# CRMEB Go 配置文件

# 服务器配置
server:
  port: 8080
  mode: debug # debug, release, test
  read_timeout: 60s
  write_timeout: 60s
  max_header_bytes: 1048576

# 数据库配置
database:
  host: ************
  port: 3306
  username: root
  password: OnZ8M5H6Zk
  database: crmeb
  charset: utf8mb4
  parse_time: true
  loc: Local
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600s
  log_level: info

# Redis配置
redis:
  host: ************
  port: 6379
  password: suxingye799
  db: 0
  pool_size: 10
  min_idle_conns: 5
  dial_timeout: 5s
  read_timeout: 3s
  write_timeout: 3s
  pool_timeout: 4s
  idle_timeout: 300s

# JWT配置
jwt:
  secret: crmeb-go-jwt-secret-key
  expire: 24h
  refresh_expire: 168h # 7天
  issuer: crmeb-go

# 日志配置
log:
  level: info # debug, info, warn, error
  format: json # json, text
  output: file # console, file, both
  file_path: ./logs/app.log
  max_size: 100 # MB
  max_age: 30 # days
  max_backups: 10
  compress: true

# 缓存配置
cache:
  default_expire: 3600s
  cleanup_interval: 600s

# 队列配置
queue:
  redis_addr: localhost:6379
  redis_password: ""
  redis_db: 1
  concurrency: 10
  queues:
    default: 6
    critical: 3
    low: 1

# 文件上传配置
upload:
  driver: local # local, oss, qiniu
  max_size: 10485760 # 10MB
  allowed_types:
    - image/jpeg
    - image/png
    - image/gif
    - image/webp
  local:
    path: ./uploads
    url_prefix: /uploads
  oss:
    endpoint: ""
    access_key_id: ""
    access_key_secret: ""
    bucket: ""
    domain: ""
  qiniu:
    access_key: ""
    secret_key: ""
    bucket: ""
    domain: ""

# 支付配置
payment:
  wechat:
    app_id: ""
    mch_id: ""
    api_key: ""
    cert_path: ""
    key_path: ""
    notify_url: ""
  alipay:
    app_id: ""
    private_key: ""
    public_key: ""
    notify_url: ""

# 短信配置
sms:
  driver: aliyun # aliyun, tencent
  aliyun:
    access_key_id: ""
    access_key_secret: ""
    sign_name: ""
    template_code: ""
  tencent:
    secret_id: ""
    secret_key: ""
    sms_sdk_app_id: ""
    sign_name: ""
    template_id: ""

# 邮件配置
email:
  host: smtp.gmail.com
  port: 587
  username: ""
  password: ""
  from: ""

# 限流配置
rate_limit:
  enabled: true
  requests: 1000
  window: 60s

# CORS配置
cors:
  allow_origins:
    - "*"
  allow_methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  allow_headers:
    - "*"
  expose_headers:
    - "*"
  allow_credentials: true
  max_age: 86400

# 监控配置
monitor:
  enabled: true
  metrics_path: /metrics
  health_path: /health

# 开发环境特殊配置
dev:
  enable_pprof: true
  enable_swagger: true
  swagger_host: localhost:8080
  swagger_base_path: /api/v1
