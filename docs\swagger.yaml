basePath: /api/v1
definitions:
  auth.RefreshTokenRequest:
    properties:
      refresh_token:
        type: string
    required:
    - refresh_token
    type: object
  auth.TokenPair:
    properties:
      access_token:
        type: string
      expires_in:
        type: integer
      refresh_token:
        type: string
    type: object
  user.ChangePasswordRequest:
    properties:
      new_password:
        maxLength: 32
        minLength: 6
        type: string
      old_password:
        type: string
    required:
    - new_password
    - old_password
    type: object
  user.CreateUserRequest:
    properties:
      account:
        maxLength: 32
        minLength: 3
        type: string
      avatar:
        maxLength: 256
        type: string
      nickname:
        maxLength: 60
        type: string
      password:
        maxLength: 32
        minLength: 6
        type: string
      phone:
        type: string
      real_name:
        maxLength: 25
        type: string
      status:
        enum:
        - 0
        - 1
        type: integer
    required:
    - account
    - nickname
    - password
    - phone
    type: object
  user.CreateUserResponse:
    properties:
      account:
        type: string
      nickname:
        type: string
      uid:
        type: integer
    type: object
  user.GetUserListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/user.UserInfoResponse'
        type: array
      page:
        type: integer
      size:
        type: integer
      total:
        type: integer
    type: object
  user.LoginRequest:
    properties:
      account:
        type: string
      password:
        type: string
    required:
    - account
    - password
    type: object
  user.LoginResponse:
    properties:
      account:
        type: string
      avatar:
        type: string
      nickname:
        type: string
      token:
        $ref: '#/definitions/auth.TokenPair'
      uid:
        type: integer
    type: object
  user.RegisterRequest:
    properties:
      account:
        maxLength: 32
        minLength: 3
        type: string
      avatar:
        maxLength: 256
        type: string
      code:
        type: string
      nickname:
        maxLength: 60
        type: string
      password:
        maxLength: 32
        minLength: 6
        type: string
      phone:
        type: string
    required:
    - account
    - code
    - password
    - phone
    type: object
  user.RegisterResponse:
    properties:
      account:
        type: string
      nickname:
        type: string
      token:
        $ref: '#/definitions/auth.TokenPair'
      uid:
        type: integer
    type: object
  user.TokenResponse:
    properties:
      token:
        $ref: '#/definitions/auth.TokenPair'
    type: object
  user.UpdateUserInfoRequest:
    properties:
      age:
        maximum: 150
        minimum: 0
        type: integer
      avatar:
        maxLength: 256
        type: string
      birthday:
        type: string
      nickname:
        maxLength: 60
        type: string
      real_name:
        maxLength: 25
        type: string
      sex:
        enum:
        - 0
        - 1
        - 2
        type: integer
    type: object
  user.UpdateUserStatusRequest:
    properties:
      status:
        enum:
        - 0
        - 1
        type: integer
    required:
    - status
    type: object
  user.UserInfoResponse:
    properties:
      account:
        type: string
      add_time:
        type: string
      age:
        type: integer
      avatar:
        type: string
      birthday:
        type: string
      brokerage_price:
        type: number
      integral:
        type: integer
      is_promoter:
        type: integer
      last_time:
        type: string
      level:
        type: integer
      nickname:
        type: string
      now_money:
        type: number
      pay_count:
        type: integer
      phone:
        type: string
      real_name:
        type: string
      sex:
        type: integer
      spread_count:
        type: integer
      status:
        type: integer
      uid:
        type: integer
    type: object
  utils.Response:
    properties:
      code:
        type: integer
      data: {}
      msg:
        type: string
      time:
        type: integer
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.crmeb.com/support
  description: CRMEB多商户系统Go版本API文档
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: CRMEB Go API
  version: "1.0"
paths:
  /admin/users:
    get:
      consumes:
      - application/json
      description: 管理员获取用户列表
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/user.GetUserListResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: 获取用户列表
      tags:
      - 管理员-用户管理
    post:
      consumes:
      - application/json
      description: 管理员创建新用户
      parameters:
      - description: 创建用户请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/user.CreateUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/user.CreateUserResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: 创建用户
      tags:
      - 管理员-用户管理
  /admin/users/{id}:
    delete:
      consumes:
      - application/json
      description: 管理员删除指定用户
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: 删除用户
      tags:
      - 管理员-用户管理
    get:
      consumes:
      - application/json
      description: 管理员获取指定用户的详细信息
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/user.UserInfoResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/utils.Response'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: 获取用户详情
      tags:
      - 管理员-用户管理
    put:
      consumes:
      - application/json
      description: 管理员更新指定用户信息
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      - description: 更新请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/user.UpdateUserInfoRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: 更新用户
      tags:
      - 管理员-用户管理
  /admin/users/{id}/status:
    put:
      consumes:
      - application/json
      description: 管理员更新指定用户的状态
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      - description: 状态更新请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/user.UpdateUserStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: 更新用户状态
      tags:
      - 管理员-用户管理
  /auth/login:
    post:
      consumes:
      - application/json
      description: 用户登录接口
      parameters:
      - description: 登录请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/user.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 登录成功
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/user.LoginResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: 认证失败
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 用户登录
      tags:
      - 认证
  /auth/logout:
    post:
      consumes:
      - application/json
      description: 用户登出接口
      produces:
      - application/json
      responses:
        "200":
          description: 登出成功
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: 用户登出
      tags:
      - 认证
  /auth/refresh:
    post:
      consumes:
      - application/json
      description: 刷新访问令牌
      parameters:
      - description: 刷新令牌请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/auth.RefreshTokenRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 刷新成功
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/user.TokenResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: 刷新令牌无效
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 刷新令牌
      tags:
      - 认证
  /auth/register:
    post:
      consumes:
      - application/json
      description: 用户注册接口
      parameters:
      - description: 注册请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/user.RegisterRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 注册成功
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/user.RegisterResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 用户注册
      tags:
      - 认证
  /user/change-password:
    post:
      consumes:
      - application/json
      description: 修改当前登录用户的密码
      parameters:
      - description: 修改密码请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/user.ChangePasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 修改成功
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: 修改密码
      tags:
      - 用户
  /user/profile:
    get:
      consumes:
      - application/json
      description: 获取当前登录用户的详细信息
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/user.UserInfoResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/utils.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: 获取用户信息
      tags:
      - 用户
    put:
      consumes:
      - application/json
      description: 更新当前登录用户的信息
      parameters:
      - description: 更新请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/user.UpdateUserInfoRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/utils.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/utils.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/utils.Response'
      security:
      - BearerAuth: []
      summary: 更新用户信息
      tags:
      - 用户
securityDefinitions:
  BearerAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
