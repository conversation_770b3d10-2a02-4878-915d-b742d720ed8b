package auth

import (
	"crmeb-go/internal/api/middleware"
	"crmeb-go/internal/container"
	userService "crmeb-go/internal/service/user"
	"crmeb-go/pkg/utils"
	"github.com/gin-gonic/gin"
)

// Register 用户注册
// @Summary 用户注册
// @Description 用户注册接口
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body userService.RegisterRequest true "注册请求"
// @Success 200 {object} utils.Response{data=userService.RegisterResponse} "注册成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /auth/register [post]
func Register(c *gin.Context) {
	var req userService.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationError(c, "请求参数错误: "+err.Error())
		return
	}

	// 获取用户服务实例
	userSvc := getUserService()
	
	resp, err := userSvc.Register(c.Request.Context(), &req)
	if err != nil {
		utils.BusinessLogicError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "注册成功", resp)
}

// Login 用户登录
// @Summary 用户登录
// @Description 用户登录接口
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body userService.LoginRequest true "登录请求"
// @Success 200 {object} utils.Response{data=userService.LoginResponse} "登录成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 401 {object} utils.Response "认证失败"
// @Router /auth/login [post]
func Login(c *gin.Context) {
	var req userService.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationError(c, "请求参数错误: "+err.Error())
		return
	}

	// 设置登录IP
	req.LoginIP = c.ClientIP()

	// 获取用户服务实例
	userSvc := getUserService()
	
	resp, err := userSvc.Login(c.Request.Context(), &req)
	if err != nil {
		utils.Unauthorized(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "登录成功", resp)
}

// Logout 用户登出
// @Summary 用户登出
// @Description 用户登出接口
// @Tags 认证
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} utils.Response "登出成功"
// @Failure 401 {object} utils.Response "未授权"
// @Router /auth/logout [post]
func Logout(c *gin.Context) {
	userID := middleware.GetUserID(c)
	token := middleware.GetToken(c)

	// 获取用户服务实例
	userSvc := getUserService()
	
	err := userSvc.Logout(c.Request.Context(), userID, token)
	if err != nil {
		utils.BusinessLogicError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "登出成功", nil)
}

// RefreshToken 刷新令牌
// @Summary 刷新令牌
// @Description 刷新访问令牌
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body RefreshTokenRequest true "刷新令牌请求"
// @Success 200 {object} utils.Response{data=userService.TokenResponse} "刷新成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 401 {object} utils.Response "刷新令牌无效"
// @Router /auth/refresh [post]
func RefreshToken(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationError(c, "请求参数错误: "+err.Error())
		return
	}

	// 获取用户服务实例
	userSvc := getUserService()
	
	resp, err := userSvc.RefreshToken(c.Request.Context(), req.RefreshToken)
	if err != nil {
		utils.Unauthorized(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "刷新成功", resp)
}

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// getUserService 获取用户服务实例
func getUserService() userService.UserService {
	return container.GetUserService()
}
