package user

import (
	"strconv"

	"crmeb-go/internal/api/middleware"
	"crmeb-go/internal/container"
	userService "crmeb-go/internal/service/user"
	"crmeb-go/pkg/utils"
	"github.com/gin-gonic/gin"
)

// GetProfile 获取用户信息
// @Summary 获取用户信息
// @Description 获取当前登录用户的详细信息
// @Tags 用户
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} utils.Response{data=userService.UserInfoResponse} "获取成功"
// @Failure 401 {object} utils.Response "未授权"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /user/profile [get]
func GetProfile(c *gin.Context) {
	userID := middleware.GetUserID(c)
	
	// 获取用户服务实例
	userSvc := getUserService()
	
	resp, err := userSvc.GetUserInfo(c.Request.Context(), userID)
	if err != nil {
		utils.BusinessLogicError(c, err.Error())
		return
	}

	utils.Success(c, resp)
}

// UpdateProfile 更新用户信息
// @Summary 更新用户信息
// @Description 更新当前登录用户的信息
// @Tags 用户
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body userService.UpdateUserInfoRequest true "更新请求"
// @Success 200 {object} utils.Response "更新成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 401 {object} utils.Response "未授权"
// @Router /user/profile [put]
func UpdateProfile(c *gin.Context) {
	var req userService.UpdateUserInfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationError(c, "请求参数错误: "+err.Error())
		return
	}

	userID := middleware.GetUserID(c)
	
	// 获取用户服务实例
	userSvc := getUserService()
	
	err := userSvc.UpdateUserInfo(c.Request.Context(), userID, &req)
	if err != nil {
		utils.BusinessLogicError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "更新成功", nil)
}

// ChangePassword 修改密码
// @Summary 修改密码
// @Description 修改当前登录用户的密码
// @Tags 用户
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body userService.ChangePasswordRequest true "修改密码请求"
// @Success 200 {object} utils.Response "修改成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 401 {object} utils.Response "未授权"
// @Router /user/change-password [post]
func ChangePassword(c *gin.Context) {
	var req userService.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationError(c, "请求参数错误: "+err.Error())
		return
	}

	userID := middleware.GetUserID(c)
	
	// 获取用户服务实例
	userSvc := getUserService()
	
	err := userSvc.ChangePassword(c.Request.Context(), userID, &req)
	if err != nil {
		utils.BusinessLogicError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "密码修改成功", nil)
}

// GetUserList 获取用户列表（管理员）
// @Summary 获取用户列表
// @Description 管理员获取用户列表
// @Tags 管理员-用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} utils.Response{data=userService.GetUserListResponse} "获取成功"
// @Failure 401 {object} utils.Response "未授权"
// @Router /admin/users [get]
func GetUserList(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	
	req := &userService.GetUserListRequest{
		Page:     page,
		PageSize: pageSize,
		Filters:  make(map[string]interface{}),
	}
	
	// 处理查询参数
	if status := c.Query("status"); status != "" {
		req.Filters["status"] = status
	}
	if userType := c.Query("user_type"); userType != "" {
		req.Filters["user_type"] = userType
	}
	if keyword := c.Query("keyword"); keyword != "" {
		req.Filters["keyword"] = keyword
	}
	
	// 获取用户服务实例
	userSvc := getUserService()
	
	resp, err := userSvc.GetUserList(c.Request.Context(), req)
	if err != nil {
		utils.BusinessLogicError(c, err.Error())
		return
	}

	utils.SuccessPage(c, resp.List, resp.Total, resp.Page, resp.Size)
}

// CreateUser 创建用户（管理员）
// @Summary 创建用户
// @Description 管理员创建新用户
// @Tags 管理员-用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body userService.CreateUserRequest true "创建用户请求"
// @Success 200 {object} utils.Response{data=userService.CreateUserResponse} "创建成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Router /admin/users [post]
func CreateUser(c *gin.Context) {
	var req userService.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationError(c, "请求参数错误: "+err.Error())
		return
	}
	
	// 获取用户服务实例
	userSvc := getUserService()
	
	resp, err := userSvc.CreateUser(c.Request.Context(), &req)
	if err != nil {
		utils.BusinessLogicError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "用户创建成功", resp)
}

// GetUserDetail 获取用户详情（管理员）
// @Summary 获取用户详情
// @Description 管理员获取指定用户的详细信息
// @Tags 管理员-用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "用户ID"
// @Success 200 {object} utils.Response{data=userService.UserInfoResponse} "获取成功"
// @Failure 400 {object} utils.Response "参数错误"
// @Failure 404 {object} utils.Response "用户不存在"
// @Router /admin/users/{id} [get]
func GetUserDetail(c *gin.Context) {
	idStr := c.Param("id")
	userID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}
	
	// 获取用户服务实例
	userSvc := getUserService()
	
	resp, err := userSvc.GetUserInfo(c.Request.Context(), uint(userID))
	if err != nil {
		utils.BusinessLogicError(c, err.Error())
		return
	}

	utils.Success(c, resp)
}

// UpdateUser 更新用户（管理员）
// @Summary 更新用户
// @Description 管理员更新指定用户信息
// @Tags 管理员-用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "用户ID"
// @Param request body userService.UpdateUserInfoRequest true "更新请求"
// @Success 200 {object} utils.Response "更新成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Router /admin/users/{id} [put]
func UpdateUser(c *gin.Context) {
	idStr := c.Param("id")
	userID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}

	var req userService.UpdateUserInfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationError(c, "请求参数错误: "+err.Error())
		return
	}
	
	// 获取用户服务实例
	userSvc := getUserService()
	
	err = userSvc.UpdateUserInfo(c.Request.Context(), uint(userID), &req)
	if err != nil {
		utils.BusinessLogicError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "用户更新成功", nil)
}

// DeleteUser 删除用户（管理员）
// @Summary 删除用户
// @Description 管理员删除指定用户
// @Tags 管理员-用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "用户ID"
// @Success 200 {object} utils.Response "删除成功"
// @Failure 400 {object} utils.Response "参数错误"
// @Router /admin/users/{id} [delete]
func DeleteUser(c *gin.Context) {
	idStr := c.Param("id")
	userID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}
	
	// 获取用户服务实例
	userSvc := getUserService()
	
	err = userSvc.DeleteUser(c.Request.Context(), uint(userID))
	if err != nil {
		utils.BusinessLogicError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "用户删除成功", nil)
}

// UpdateUserStatus 更新用户状态（管理员）
// @Summary 更新用户状态
// @Description 管理员更新指定用户的状态
// @Tags 管理员-用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "用户ID"
// @Param request body UpdateUserStatusRequest true "状态更新请求"
// @Success 200 {object} utils.Response "更新成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Router /admin/users/{id}/status [put]
func UpdateUserStatus(c *gin.Context) {
	idStr := c.Param("id")
	userID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}

	var req UpdateUserStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationError(c, "请求参数错误: "+err.Error())
		return
	}
	
	// 获取用户服务实例
	userSvc := getUserService()
	
	err = userSvc.UpdateUserStatus(c.Request.Context(), uint(userID), req.Status)
	if err != nil {
		utils.BusinessLogicError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "用户状态更新成功", nil)
}

// UpdateUserStatusRequest 更新用户状态请求
type UpdateUserStatusRequest struct {
	Status int8 `json:"status" binding:"required,oneof=0 1"`
}

// getUserService 获取用户服务实例
func getUserService() userService.UserService {
	return container.GetUserService()
}
