package middleware

import (
	"strings"

	"crmeb-go/pkg/auth"
	"crmeb-go/pkg/utils"
	"github.com/gin-gonic/gin"
)

// AuthMiddleware JWT认证中间件
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			utils.Unauthorized(c, "缺少认证令牌")
			c.Abort()
			return
		}

		// 检查Bearer前缀
		if !strings.HasPrefix(authHeader, "Bearer ") {
			utils.Unauthorized(c, "认证令牌格式错误")
			c.Abort()
			return
		}

		// 提取令牌
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == "" {
			utils.Unauthorized(c, "认证令牌为空")
			c.Abort()
			return
		}

		// 验证令牌
		jwtManager := auth.GetGlobalJWTManager()
		claims, err := jwtManager.ValidateToken(tokenString)
		if err != nil {
			utils.Unauthorized(c, "认证令牌无效: "+err.<PERSON>rror())
			c.Abort()
			return
		}

		// 检查令牌是否在黑名单中
		if jwtManager.IsTokenBlacklisted(tokenString) {
			utils.Unauthorized(c, "认证令牌已失效")
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("user_type", claims.UserType)
		c.Set("merchant_id", claims.MerchantID)
		c.Set("token", tokenString)

		c.Next()
	}
}

// OptionalAuthMiddleware 可选认证中间件
func OptionalAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		// 检查Bearer前缀
		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.Next()
			return
		}

		// 提取令牌
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == "" {
			c.Next()
			return
		}

		// 验证令牌
		jwtManager := auth.GetGlobalJWTManager()
		claims, err := jwtManager.ValidateToken(tokenString)
		if err != nil {
			c.Next()
			return
		}

		// 检查令牌是否在黑名单中
		if jwtManager.IsTokenBlacklisted(tokenString) {
			c.Next()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("user_type", claims.UserType)
		c.Set("merchant_id", claims.MerchantID)
		c.Set("token", tokenString)

		c.Next()
	}
}

// AdminAuthMiddleware 管理员认证中间件
func AdminAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 先执行基础认证
		AuthMiddleware()(c)
		if c.IsAborted() {
			return
		}

		// 检查用户类型
		userType, exists := c.Get("user_type")
		if !exists || userType != "admin" {
			utils.Forbidden(c, "需要管理员权限")
			c.Abort()
			return
		}

		c.Next()
	}
}

// MerchantAuthMiddleware 商户认证中间件
func MerchantAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 先执行基础认证
		AuthMiddleware()(c)
		if c.IsAborted() {
			return
		}

		// 检查用户类型
		userType, exists := c.Get("user_type")
		if !exists || userType != "merchant" {
			utils.Forbidden(c, "需要商户权限")
			c.Abort()
			return
		}

		// 检查商户ID
		merchantID, exists := c.Get("merchant_id")
		if !exists || merchantID.(uint) == 0 {
			utils.Forbidden(c, "无效的商户信息")
			c.Abort()
			return
		}

		c.Next()
	}
}

// UserAuthMiddleware 用户认证中间件
func UserAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 先执行基础认证
		AuthMiddleware()(c)
		if c.IsAborted() {
			return
		}

		// 检查用户类型
		userType, exists := c.Get("user_type")
		if !exists || userType != "user" {
			utils.Forbidden(c, "需要用户权限")
			c.Abort()
			return
		}

		c.Next()
	}
}

// GetUserID 从上下文中获取用户ID
func GetUserID(c *gin.Context) uint {
	if userID, exists := c.Get("user_id"); exists {
		return userID.(uint)
	}
	return 0
}

// GetUsername 从上下文中获取用户名
func GetUsername(c *gin.Context) string {
	if username, exists := c.Get("username"); exists {
		return username.(string)
	}
	return ""
}

// GetUserType 从上下文中获取用户类型
func GetUserType(c *gin.Context) string {
	if userType, exists := c.Get("user_type"); exists {
		return userType.(string)
	}
	return ""
}

// GetMerchantID 从上下文中获取商户ID
func GetMerchantID(c *gin.Context) uint {
	if merchantID, exists := c.Get("merchant_id"); exists {
		return merchantID.(uint)
	}
	return 0
}

// GetToken 从上下文中获取令牌
func GetToken(c *gin.Context) string {
	if token, exists := c.Get("token"); exists {
		return token.(string)
	}
	return ""
}

// IsAuthenticated 检查是否已认证
func IsAuthenticated(c *gin.Context) bool {
	_, exists := c.Get("user_id")
	return exists
}

// IsAdmin 检查是否为管理员
func IsAdmin(c *gin.Context) bool {
	return GetUserType(c) == "admin"
}

// IsMerchant 检查是否为商户
func IsMerchant(c *gin.Context) bool {
	return GetUserType(c) == "merchant"
}

// IsUser 检查是否为普通用户
func IsUser(c *gin.Context) bool {
	return GetUserType(c) == "user"
}

// RequirePermission 权限检查中间件
func RequirePermission(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 先检查是否已认证
		if !IsAuthenticated(c) {
			utils.Unauthorized(c, "需要登录")
			c.Abort()
			return
		}

		// 这里可以实现更复杂的权限检查逻辑
		// 例如从数据库中查询用户权限
		userID := GetUserID(c)
		userType := GetUserType(c)

		// 简单的权限检查示例
		if !hasPermission(userID, userType, permission) {
			utils.Forbidden(c, "权限不足")
			c.Abort()
			return
		}

		c.Next()
	}
}

// hasPermission 检查用户是否有指定权限
func hasPermission(userID uint, userType, permission string) bool {
	// 这里应该实现实际的权限检查逻辑
	// 例如查询数据库中的用户权限表
	
	// 管理员拥有所有权限
	if userType == "admin" {
		return true
	}

	// 这里可以添加更复杂的权限检查逻辑
	// 例如基于角色的权限控制(RBAC)
	
	return false
}
