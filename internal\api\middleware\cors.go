package middleware

import (
	"net/http"
	"strings"

	"crmeb-go/internal/config"
	"github.com/gin-gonic/gin"
)

// CORSMiddleware CORS跨域中间件
func CORSMiddleware(cfg *config.CORSConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")
		
		// 检查是否允许该来源
		if isAllowedOrigin(origin, cfg.AllowOrigins) {
			c.<PERSON>er("Access-Control-Allow-Origin", origin)
		}

		// 设置允许的方法
		if len(cfg.AllowMethods) > 0 {
			c.<PERSON>er("Access-Control-Allow-Methods", strings.Join(cfg.AllowMethods, ", "))
		}

		// 设置允许的头部
		if len(cfg.AllowHeaders) > 0 {
			c.<PERSON>er("Access-Control-Allow-Headers", strings.Join(cfg.AllowHeaders, ", "))
		}

		// 设置暴露的头部
		if len(cfg.ExposeHeaders) > 0 {
			c.Head<PERSON>("Access-Control-Expose-Headers", strings.Join(cfg.ExposeHeaders, ", "))
		}

		// 设置是否允许凭证
		if cfg.AllowCredentials {
			c.Header("Access-Control-Allow-Credentials", "true")
		}

		// 设置预检请求的缓存时间
		if cfg.MaxAge > 0 {
			c.Header("Access-Control-Max-Age", string(rune(cfg.MaxAge)))
		}

		// 处理预检请求
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// isAllowedOrigin 检查是否允许该来源
func isAllowedOrigin(origin string, allowedOrigins []string) bool {
	for _, allowedOrigin := range allowedOrigins {
		if allowedOrigin == "*" {
			return true
		}
		if allowedOrigin == origin {
			return true
		}
		// 支持通配符匹配
		if strings.HasSuffix(allowedOrigin, "*") {
			prefix := strings.TrimSuffix(allowedOrigin, "*")
			if strings.HasPrefix(origin, prefix) {
				return true
			}
		}
	}
	return false
}
