package middleware

import (
	"bytes"
	"io"
	"strings"
	"time"

	"crmeb-go/pkg/logger"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// LoggerMiddleware 日志中间件
func LoggerMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		// 自定义日志格式
		return ""
	})
}

// RequestLoggerMiddleware 请求日志中间件
func RequestLoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 生成请求ID
		requestID := uuid.New().String()
		c.Set("request_id", requestID)

		// 记录开始时间
		start := time.Now()

		// 获取用户信息
		userID := GetUserID(c)
		var userIDStr string
		if userID > 0 {
			userIDStr = string(rune(userID))
		}

		// 创建请求日志记录器
		reqLogger := logger.NewRequestLogger(
			requestID,
			userIDStr,
			c.Request.Method,
			c.Request.URL.Path,
		)

		// 记录请求开始
		reqLogger.WithFields(map[string]interface{}{
			"query_params": c.Request.URL.RawQuery,
			"user_agent":   c.Request.UserAgent(),
			"client_ip":    c.ClientIP(),
		}).Info("Request started")

		// 如果需要记录请求体，可以在这里添加
		if shouldLogRequestBody(c) {
			body := getRequestBody(c)
			if body != "" {
				reqLogger.WithField("request_body", body).Debug("Request body")
			}
		}

		// 处理请求
		c.Next()

		// 计算处理时间
		duration := time.Since(start)

		// 记录请求完成
		reqLogger.LogRequest(c.Writer.Status(), duration, c.ClientIP())

		// 如果有错误，记录错误信息
		if len(c.Errors) > 0 {
			for _, err := range c.Errors {
				reqLogger.LogError(err.Err, "Request error")
			}
		}
	}
}

// shouldLogRequestBody 判断是否应该记录请求体
func shouldLogRequestBody(c *gin.Context) bool {
	// 只记录POST、PUT、PATCH请求的请求体
	method := c.Request.Method
	return method == "POST" || method == "PUT" || method == "PATCH"
}

// getRequestBody 获取请求体内容
func getRequestBody(c *gin.Context) string {
	if c.Request.Body == nil {
		return ""
	}

	// 读取请求体
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return ""
	}

	// 重新设置请求体，以便后续处理
	c.Request.Body = io.NopCloser(bytes.NewBuffer(body))

	// 限制日志记录的请求体大小
	const maxBodySize = 1024 // 1KB
	if len(body) > maxBodySize {
		return string(body[:maxBodySize]) + "... (truncated)"
	}

	return string(body)
}

// ErrorLoggerMiddleware 错误日志中间件
func ErrorLoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 检查是否有错误
		if len(c.Errors) > 0 {
			requestID, _ := c.Get("request_id")
			userID := GetUserID(c)
			
			var userIDStr string
			if userID > 0 {
				userIDStr = string(rune(userID))
			}

			reqLogger := logger.NewRequestLogger(
				requestID.(string),
				userIDStr,
				c.Request.Method,
				c.Request.URL.Path,
			)

			// 记录所有错误
			for _, ginErr := range c.Errors {
				reqLogger.WithFields(map[string]interface{}{
					"error_type": ginErr.Type,
					"error_meta": ginErr.Meta,
				}).Error("Request processing error: ", ginErr.Err)
			}
		}
	}
}

// RecoveryLoggerMiddleware 恢复日志中间件
func RecoveryLoggerMiddleware() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		requestID, _ := c.Get("request_id")
		userID := GetUserID(c)
		
		var userIDStr string
		if userID > 0 {
			userIDStr = string(rune(userID))
		}

		reqLogger := logger.NewRequestLogger(
			requestID.(string),
			userIDStr,
			c.Request.Method,
			c.Request.URL.Path,
		)

		// 记录panic信息
		reqLogger.WithFields(map[string]interface{}{
			"panic_value": recovered,
			"client_ip":   c.ClientIP(),
			"user_agent":  c.Request.UserAgent(),
		}).Error("Request panic recovered")

		// 返回500错误
		c.JSON(500, gin.H{
			"code":    500,
			"message": "服务器内部错误",
			"time":    time.Now().Unix(),
		})
	})
}

// GetRequestID 从上下文中获取请求ID
func GetRequestID(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		return requestID.(string)
	}
	return ""
}

// SetRequestID 设置请求ID到上下文
func SetRequestID(c *gin.Context, requestID string) {
	c.Set("request_id", requestID)
}

// BusinessLoggerMiddleware 业务日志中间件
func BusinessLoggerMiddleware(module string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 创建业务日志记录器
		bizLogger := logger.NewBusinessLogger(module, c.Request.URL.Path)
		
		// 将业务日志记录器存储到上下文中
		c.Set("business_logger", bizLogger)
		
		c.Next()
	}
}

// GetBusinessLogger 从上下文中获取业务日志记录器
func GetBusinessLogger(c *gin.Context) *logger.BusinessLogger {
	if bizLogger, exists := c.Get("business_logger"); exists {
		return bizLogger.(*logger.BusinessLogger)
	}
	return nil
}

// SecurityLoggerMiddleware 安全日志中间件
func SecurityLoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录安全相关的请求
		if isSecuritySensitiveRequest(c) {
			userID := GetUserID(c)
			var userIDStr string
			if userID > 0 {
				userIDStr = string(rune(userID))
			}

			secLogger := logger.NewSecurityLogger(
				"request",
				userIDStr,
				c.ClientIP(),
			)

			secLogger.LogSecurityEvent("info", "Security sensitive request", map[string]interface{}{
				"method":     c.Request.Method,
				"path":       c.Request.URL.Path,
				"user_agent": c.Request.UserAgent(),
			})
		}

		c.Next()
	}
}

// isSecuritySensitiveRequest 判断是否为安全敏感请求
func isSecuritySensitiveRequest(c *gin.Context) bool {
	path := c.Request.URL.Path
	
	// 定义安全敏感的路径模式
	sensitivePatterns := []string{
		"/api/auth/login",
		"/api/auth/register",
		"/api/auth/logout",
		"/api/user/password",
		"/api/admin/",
		"/api/merchant/",
	}

	for _, pattern := range sensitivePatterns {
		if strings.Contains(path, pattern) {
			return true
		}
	}

	return false
}
