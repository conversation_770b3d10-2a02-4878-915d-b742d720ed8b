package routes

import (
	"net/http"
	"net/http/pprof"

	"crmeb-go/internal/api/handlers/auth"
	"crmeb-go/internal/api/handlers/user"
	"crmeb-go/internal/api/middleware"
	"crmeb-go/internal/config"
	"crmeb-go/pkg/utils"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// SetupRoutes 设置路由
func SetupRoutes(engine *gin.Engine, cfg *config.Config) {
	// 全局中间件
	engine.Use(middleware.CORSMiddleware(&cfg.CORS))
	engine.Use(middleware.RequestLoggerMiddleware())
	engine.Use(middleware.ErrorLoggerMiddleware())
	engine.Use(middleware.RecoveryLoggerMiddleware())

	// 健康检查
	engine.GET("/health", func(c *gin.Context) {
		utils.Success(c, gin.H{
			"status": "ok",
			"time":   "2024-01-01 00:00:00",
		})
	})

	// API路由组 - 与PHP版本保持一致
	apiGroup := engine.Group("/api")
	{
		// 认证路由（无需认证） - 直接在/api下，与PHP版本一致
		apiGroup.POST("/login", auth.Login)
		apiGroup.POST("/register", auth.Register)
		apiGroup.POST("/logout", middleware.AuthMiddleware(), auth.Logout)

		// V1版本路由组（保留原有结构）
		apiV1 := apiGroup.Group("/v1")
		{
			// 认证路由
			authGroup := apiV1.Group("/auth")
			{
				authGroup.POST("/register", auth.Register)
				authGroup.POST("/login", auth.Login)
				authGroup.POST("/refresh", auth.RefreshToken)
				authGroup.POST("/logout", middleware.AuthMiddleware(), auth.Logout)
			}

			// 用户路由（需要认证）
			userGroup := apiV1.Group("/user")
			userGroup.Use(middleware.UserAuthMiddleware())
			{
				userGroup.GET("/profile", user.GetProfile)
				userGroup.PUT("/profile", user.UpdateProfile)
				userGroup.POST("/change-password", user.ChangePassword)
			}

			// 管理员路由
			adminGroup := apiV1.Group("/admin")
			adminGroup.Use(middleware.AdminAuthMiddleware())
			{
				// 用户管理
				adminUserGroup := adminGroup.Group("/users")
				{
					adminUserGroup.GET("", user.GetUserList)
					adminUserGroup.POST("", user.CreateUser)
					adminUserGroup.GET("/:id", user.GetUserDetail)
					adminUserGroup.PUT("/:id", user.UpdateUser)
					adminUserGroup.DELETE("/:id", user.DeleteUser)
					adminUserGroup.PUT("/:id/status", user.UpdateUserStatus)
				}
			}

			// 商户路由
			merchantGroup := apiV1.Group("/merchant")
			merchantGroup.Use(middleware.MerchantAuthMiddleware())
			{
				// 商户信息
				merchantGroup.GET("/profile", func(c *gin.Context) {
					utils.Success(c, gin.H{"message": "merchant profile"})
				})
			}
		}
	}

	// 开发环境特殊配置
	if cfg.Dev.EnableSwagger {
		// Swagger文档
		engine.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	}

	if cfg.Dev.EnablePprof {
		// pprof性能分析
		setupPprofRoutes(engine)
	}
}

// setupPprofRoutes 设置pprof路由
func setupPprofRoutes(engine *gin.Engine) {
	pprofGroup := engine.Group("/debug/pprof")
	{
		pprofGroup.GET("/", gin.WrapF(func(w http.ResponseWriter, r *http.Request) {
			http.Redirect(w, r, "/debug/pprof/", http.StatusMovedPermanently)
		}))
		pprofGroup.GET("/cmdline", gin.WrapF(pprof.Cmdline))
		pprofGroup.GET("/profile", gin.WrapF(pprof.Profile))
		pprofGroup.POST("/symbol", gin.WrapF(pprof.Symbol))
		pprofGroup.GET("/symbol", gin.WrapF(pprof.Symbol))
		pprofGroup.GET("/trace", gin.WrapF(pprof.Trace))
		pprofGroup.GET("/allocs", gin.WrapH(pprof.Handler("allocs")))
		pprofGroup.GET("/block", gin.WrapH(pprof.Handler("block")))
		pprofGroup.GET("/goroutine", gin.WrapH(pprof.Handler("goroutine")))
		pprofGroup.GET("/heap", gin.WrapH(pprof.Handler("heap")))
		pprofGroup.GET("/mutex", gin.WrapH(pprof.Handler("mutex")))
		pprofGroup.GET("/threadcreate", gin.WrapH(pprof.Handler("threadcreate")))
	}
}
