package config

import (
	"time"

	"github.com/spf13/viper"
)

// Config 应用配置结构
type Config struct {
	Server   ServerConfig   `mapstructure:"server"`
	Database DatabaseConfig `mapstructure:"database"`
	Redis    RedisConfig    `mapstructure:"redis"`
	JWT      JWTConfig      `mapstructure:"jwt"`
	Log      LogConfig      `mapstructure:"log"`
	Cache    CacheConfig    `mapstructure:"cache"`
	Queue    QueueConfig    `mapstructure:"queue"`
	Upload   UploadConfig   `mapstructure:"upload"`
	Payment  PaymentConfig  `mapstructure:"payment"`
	SMS      SMSConfig      `mapstructure:"sms"`
	Email    EmailConfig    `mapstructure:"email"`
	RateLimit RateLimitConfig `mapstructure:"rate_limit"`
	CORS     CORSConfig     `mapstructure:"cors"`
	Monitor  MonitorConfig  `mapstructure:"monitor"`
	Dev      DevConfig      `mapstructure:"dev"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port           int           `mapstructure:"port"`
	Mode           string        `mapstructure:"mode"`
	ReadTimeout    time.Duration `mapstructure:"read_timeout"`
	WriteTimeout   time.Duration `mapstructure:"write_timeout"`
	MaxHeaderBytes int           `mapstructure:"max_header_bytes"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host            string        `mapstructure:"host"`
	Port            int           `mapstructure:"port"`
	Username        string        `mapstructure:"username"`
	Password        string        `mapstructure:"password"`
	Database        string        `mapstructure:"database"`
	Charset         string        `mapstructure:"charset"`
	ParseTime       bool          `mapstructure:"parse_time"`
	Loc             string        `mapstructure:"loc"`
	MaxIdleConns    int           `mapstructure:"max_idle_conns"`
	MaxOpenConns    int           `mapstructure:"max_open_conns"`
	ConnMaxLifetime time.Duration `mapstructure:"conn_max_lifetime"`
	LogLevel        string        `mapstructure:"log_level"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host         string        `mapstructure:"host"`
	Port         int           `mapstructure:"port"`
	Password     string        `mapstructure:"password"`
	DB           int           `mapstructure:"db"`
	PoolSize     int           `mapstructure:"pool_size"`
	MinIdleConns int           `mapstructure:"min_idle_conns"`
	DialTimeout  time.Duration `mapstructure:"dial_timeout"`
	ReadTimeout  time.Duration `mapstructure:"read_timeout"`
	WriteTimeout time.Duration `mapstructure:"write_timeout"`
	PoolTimeout  time.Duration `mapstructure:"pool_timeout"`
	IdleTimeout  time.Duration `mapstructure:"idle_timeout"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret        string        `mapstructure:"secret"`
	Expire        time.Duration `mapstructure:"expire"`
	RefreshExpire time.Duration `mapstructure:"refresh_expire"`
	Issuer        string        `mapstructure:"issuer"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string `mapstructure:"level"`
	Format     string `mapstructure:"format"`
	Output     string `mapstructure:"output"`
	FilePath   string `mapstructure:"file_path"`
	MaxSize    int    `mapstructure:"max_size"`
	MaxAge     int    `mapstructure:"max_age"`
	MaxBackups int    `mapstructure:"max_backups"`
	Compress   bool   `mapstructure:"compress"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	DefaultExpire   time.Duration `mapstructure:"default_expire"`
	CleanupInterval time.Duration `mapstructure:"cleanup_interval"`
}

// QueueConfig 队列配置
type QueueConfig struct {
	RedisAddr     string            `mapstructure:"redis_addr"`
	RedisPassword string            `mapstructure:"redis_password"`
	RedisDB       int               `mapstructure:"redis_db"`
	Concurrency   int               `mapstructure:"concurrency"`
	Queues        map[string]int    `mapstructure:"queues"`
}

// UploadConfig 文件上传配置
type UploadConfig struct {
	Driver       string   `mapstructure:"driver"`
	MaxSize      int64    `mapstructure:"max_size"`
	AllowedTypes []string `mapstructure:"allowed_types"`
	Local        LocalUploadConfig  `mapstructure:"local"`
	OSS          OSSUploadConfig    `mapstructure:"oss"`
	Qiniu        QiniuUploadConfig  `mapstructure:"qiniu"`
}

// LocalUploadConfig 本地上传配置
type LocalUploadConfig struct {
	Path      string `mapstructure:"path"`
	URLPrefix string `mapstructure:"url_prefix"`
}

// OSSUploadConfig 阿里云OSS配置
type OSSUploadConfig struct {
	Endpoint        string `mapstructure:"endpoint"`
	AccessKeyID     string `mapstructure:"access_key_id"`
	AccessKeySecret string `mapstructure:"access_key_secret"`
	Bucket          string `mapstructure:"bucket"`
	Domain          string `mapstructure:"domain"`
}

// QiniuUploadConfig 七牛云配置
type QiniuUploadConfig struct {
	AccessKey string `mapstructure:"access_key"`
	SecretKey string `mapstructure:"secret_key"`
	Bucket    string `mapstructure:"bucket"`
	Domain    string `mapstructure:"domain"`
}

// PaymentConfig 支付配置
type PaymentConfig struct {
	Wechat WechatPayConfig `mapstructure:"wechat"`
	Alipay AlipayConfig    `mapstructure:"alipay"`
}

// WechatPayConfig 微信支付配置
type WechatPayConfig struct {
	AppID     string `mapstructure:"app_id"`
	MchID     string `mapstructure:"mch_id"`
	APIKey    string `mapstructure:"api_key"`
	CertPath  string `mapstructure:"cert_path"`
	KeyPath   string `mapstructure:"key_path"`
	NotifyURL string `mapstructure:"notify_url"`
}

// AlipayConfig 支付宝配置
type AlipayConfig struct {
	AppID      string `mapstructure:"app_id"`
	PrivateKey string `mapstructure:"private_key"`
	PublicKey  string `mapstructure:"public_key"`
	NotifyURL  string `mapstructure:"notify_url"`
}

// SMSConfig 短信配置
type SMSConfig struct {
	Driver  string           `mapstructure:"driver"`
	Aliyun  AliyunSMSConfig  `mapstructure:"aliyun"`
	Tencent TencentSMSConfig `mapstructure:"tencent"`
}

// AliyunSMSConfig 阿里云短信配置
type AliyunSMSConfig struct {
	AccessKeyID     string `mapstructure:"access_key_id"`
	AccessKeySecret string `mapstructure:"access_key_secret"`
	SignName        string `mapstructure:"sign_name"`
	TemplateCode    string `mapstructure:"template_code"`
}

// TencentSMSConfig 腾讯云短信配置
type TencentSMSConfig struct {
	SecretID     string `mapstructure:"secret_id"`
	SecretKey    string `mapstructure:"secret_key"`
	SMSSDKAppID  string `mapstructure:"sms_sdk_app_id"`
	SignName     string `mapstructure:"sign_name"`
	TemplateID   string `mapstructure:"template_id"`
}

// EmailConfig 邮件配置
type EmailConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	From     string `mapstructure:"from"`
}

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	Enabled  bool          `mapstructure:"enabled"`
	Requests int           `mapstructure:"requests"`
	Window   time.Duration `mapstructure:"window"`
}

// CORSConfig CORS配置
type CORSConfig struct {
	AllowOrigins     []string `mapstructure:"allow_origins"`
	AllowMethods     []string `mapstructure:"allow_methods"`
	AllowHeaders     []string `mapstructure:"allow_headers"`
	ExposeHeaders    []string `mapstructure:"expose_headers"`
	AllowCredentials bool     `mapstructure:"allow_credentials"`
	MaxAge           int      `mapstructure:"max_age"`
}

// MonitorConfig 监控配置
type MonitorConfig struct {
	Enabled     bool   `mapstructure:"enabled"`
	MetricsPath string `mapstructure:"metrics_path"`
	HealthPath  string `mapstructure:"health_path"`
}

// DevConfig 开发环境配置
type DevConfig struct {
	EnablePprof     bool   `mapstructure:"enable_pprof"`
	EnableSwagger   bool   `mapstructure:"enable_swagger"`
	SwaggerHost     string `mapstructure:"swagger_host"`
	SwaggerBasePath string `mapstructure:"swagger_base_path"`
}

// LoadConfig 加载配置
func LoadConfig(configPath string) (*Config, error) {
	viper.SetConfigFile(configPath)
	viper.AutomaticEnv()

	if err := viper.ReadInConfig(); err != nil {
		return nil, err
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, err
	}

	return &config, nil
}

// GetConfig 获取全局配置实例
var globalConfig *Config

func SetGlobalConfig(config *Config) {
	globalConfig = config
}

func GetGlobalConfig() *Config {
	return globalConfig
}
