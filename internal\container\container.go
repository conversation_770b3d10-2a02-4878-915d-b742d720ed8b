package container

import (
	"crmeb-go/internal/config"
	userRepo "crmeb-go/internal/repository/user"
	userService "crmeb-go/internal/service/user"
	"crmeb-go/pkg/auth"
	"crmeb-go/pkg/cache"
)

// Container 依赖注入容器
type Container struct {
	// 配置
	Config *config.Config

	// 基础服务
	Cache      cache.Cache
	JWTManager *auth.JWTManager

	// 仓储层
	UserRepo     userRepo.UserRepository
	UserBillRepo userRepo.UserBillRepository

	// 服务层
	UserService userService.UserService
}

// NewContainer 创建容器实例
func NewContainer(cfg *config.Config) *Container {
	container := &Container{
		Config: cfg,
	}

	// 初始化基础服务
	container.initBaseServices()

	// 初始化仓储层
	container.initRepositories()

	// 初始化服务层
	container.initServices()

	return container
}

// initBaseServices 初始化基础服务
func (c *Container) initBaseServices() {
	// 初始化缓存
	redisClient := cache.GetRedisClient()
	c.Cache = cache.NewRedisCache(redisClient)

	// 初始化JWT管理器
	c.JWTManager = auth.GetGlobalJWTManager()
}

// initRepositories 初始化仓储层
func (c *Container) initRepositories() {
	c.UserRepo = userRepo.NewUserRepository()
	c.UserBillRepo = userRepo.NewUserBillRepository()
}

// initServices 初始化服务层
func (c *Container) initServices() {
	c.UserService = userService.NewUserService(
		c.UserRepo,
		c.UserBillRepo,
		c.Cache,
		c.JWTManager,
	)
}

// 全局容器实例
var globalContainer *Container

// SetGlobalContainer 设置全局容器
func SetGlobalContainer(container *Container) {
	globalContainer = container
}

// GetGlobalContainer 获取全局容器
func GetGlobalContainer() *Container {
	return globalContainer
}

// GetUserService 获取用户服务
func GetUserService() userService.UserService {
	if globalContainer != nil {
		return globalContainer.UserService
	}
	return nil
}
