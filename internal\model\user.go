package model

import (
	"time"

	"gorm.io/gorm"
)

// User 用户模型
type User struct {
	UID              uint           `gorm:"primaryKey;column:uid" json:"uid"`
	Account          string         `gorm:"column:account;size:32;uniqueIndex;not null" json:"account"`
	Pwd              string         `gorm:"column:pwd;size:255;not null" json:"-"`
	RealName         string         `gorm:"column:real_name;size:25" json:"real_name"`
	Birthday         *time.Time     `gorm:"column:birthday" json:"birthday"`
	CardID           string         `gorm:"column:card_id;size:20" json:"card_id"`
	Mark             string         `gorm:"column:mark;size:255" json:"mark"`
	PartnerID        uint           `gorm:"column:partner_id;default:0" json:"partner_id"`
	GroupID          uint           `gorm:"column:group_id;default:0" json:"group_id"`
	Nickname         string         `gorm:"column:nickname;size:60;not null" json:"nickname"`
	Avatar           string         `gorm:"column:avatar;size:256" json:"avatar"`
	Phone            string         `gorm:"column:phone;size:15;index" json:"phone"`
	AddTime          time.Time      `gorm:"column:add_time;not null" json:"add_time"`
	AddIP            string         `gorm:"column:add_ip;size:16" json:"add_ip"`
	LastTime         time.Time      `gorm:"column:last_time;not null" json:"last_time"`
	LastIP           string         `gorm:"column:last_ip;size:16" json:"last_ip"`
	NowMoney         float64        `gorm:"column:now_money;type:decimal(8,2);default:0.00" json:"now_money"`
	BrokeragePrice   float64        `gorm:"column:brokerage_price;type:decimal(8,2);default:0.00" json:"brokerage_price"`
	Integral         int            `gorm:"column:integral;default:0" json:"integral"`
	SignNum          int            `gorm:"column:sign_num;default:0" json:"sign_num"`
	Status           int8           `gorm:"column:status;default:1" json:"status"`
	Level            int            `gorm:"column:level;default:0" json:"level"`
	SpreadUID        uint           `gorm:"column:spread_uid;default:0" json:"spread_uid"`
	SpreadTime       *time.Time     `gorm:"column:spread_time" json:"spread_time"`
	UserType         string         `gorm:"column:user_type;size:32;default:'h5'" json:"user_type"`
	IsPromoter       int8           `gorm:"column:is_promoter;default:0" json:"is_promoter"`
	PayCount         int            `gorm:"column:pay_count;default:0" json:"pay_count"`
	SpreadCount      int            `gorm:"column:spread_count;default:0" json:"spread_count"`
	CleanTime        *time.Time     `gorm:"column:clean_time" json:"clean_time"`
	AddressID        uint           `gorm:"column:address_id;default:0" json:"address_id"`
	LoginType        string         `gorm:"column:login_type;size:36" json:"login_type"`
	WechatUserID     uint           `gorm:"column:wechat_user_id;default:0" json:"wechat_user_id"`
	Sex              int8           `gorm:"column:sex;default:0" json:"sex"`
	Age              int            `gorm:"column:age;default:0" json:"age"`
	BrokerageLevel   int            `gorm:"column:brokerage_level;default:0" json:"brokerage_level"`
	MemberValue      int            `gorm:"column:member_value;default:0" json:"member_value"`
	MemberLevel      int            `gorm:"column:member_level;default:0" json:"member_level"`
	IsSvip           int8           `gorm:"column:is_svip;default:-1" json:"is_svip"`
	SvipEndtime      *time.Time     `gorm:"column:svip_endtime" json:"svip_endtime"`
	TopUID           uint           `gorm:"column:top_uid;default:0" json:"top_uid"`
	PromoterTime     *time.Time     `gorm:"column:promoter_time" json:"promoter_time"`
	CreatedAt        time.Time      `gorm:"column:create_time" json:"created_at"`
	UpdatedAt        time.Time      `gorm:"column:update_time" json:"updated_at"`
	DeletedAt        gorm.DeletedAt `gorm:"column:delete_time;index" json:"-"`
}

// TableName 指定表名
func (User) TableName() string {
	return "user"
}

// BeforeCreate 创建前钩子
func (u *User) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	u.AddTime = now
	u.LastTime = now
	return nil
}

// BeforeUpdate 更新前钩子
func (u *User) BeforeUpdate(tx *gorm.DB) error {
	u.LastTime = time.Now()
	return nil
}

// UserBill 用户账单模型
type UserBill struct {
	ID        uint           `gorm:"primaryKey;column:id" json:"id"`
	UID       uint           `gorm:"column:uid;not null;index" json:"uid"`
	LinkID    uint           `gorm:"column:link_id;default:0" json:"link_id"`
	PM        int8           `gorm:"column:pm;default:0" json:"pm"`
	Title     string         `gorm:"column:title;size:64;not null" json:"title"`
	Category  string         `gorm:"column:category;size:64;not null" json:"category"`
	Type      string         `gorm:"column:type;size:64;not null" json:"type"`
	Number    float64        `gorm:"column:number;type:decimal(8,2);not null" json:"number"`
	Balance   float64        `gorm:"column:balance;type:decimal(8,2);not null" json:"balance"`
	Mark      string         `gorm:"column:mark;size:512" json:"mark"`
	Status    int8           `gorm:"column:status;default:1" json:"status"`
	CreatedAt time.Time      `gorm:"column:add_time" json:"created_at"`
	UpdatedAt time.Time      `gorm:"column:update_time" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (UserBill) TableName() string {
	return "user_bill"
}

// UserAddress 用户地址模型
type UserAddress struct {
	ID         uint           `gorm:"primaryKey;column:id" json:"id"`
	UID        uint           `gorm:"column:uid;not null;index" json:"uid"`
	RealName   string         `gorm:"column:real_name;size:32;not null" json:"real_name"`
	Phone      string         `gorm:"column:phone;size:16;not null" json:"phone"`
	Province   string         `gorm:"column:province;size:64;not null" json:"province"`
	City       string         `gorm:"column:city;size:64;not null" json:"city"`
	District   string         `gorm:"column:district;size:64;not null" json:"district"`
	Detail     string         `gorm:"column:detail;size:256;not null" json:"detail"`
	PostCode   string         `gorm:"column:post_code;size:10" json:"post_code"`
	Longitude  string         `gorm:"column:longitude;size:16" json:"longitude"`
	Latitude   string         `gorm:"column:latitude;size:16" json:"latitude"`
	IsDefault  int8           `gorm:"column:is_default;default:0" json:"is_default"`
	CreatedAt  time.Time      `gorm:"column:add_time" json:"created_at"`
	UpdatedAt  time.Time      `gorm:"column:update_time" json:"updated_at"`
	DeletedAt  gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (UserAddress) TableName() string {
	return "user_address"
}

// UserGroup 用户分组模型
type UserGroup struct {
	ID        uint           `gorm:"primaryKey;column:id" json:"id"`
	GroupName string         `gorm:"column:group_name;size:64;not null" json:"group_name"`
	CreatedAt time.Time      `gorm:"column:add_time" json:"created_at"`
	UpdatedAt time.Time      `gorm:"column:update_time" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (UserGroup) TableName() string {
	return "user_group"
}

// UserLevel 用户等级模型
type UserLevel struct {
	ID          uint           `gorm:"primaryKey;column:id" json:"id"`
	UID         uint           `gorm:"column:uid;not null;index" json:"uid"`
	LevelID     uint           `gorm:"column:level_id;not null" json:"level_id"`
	Grade       int            `gorm:"column:grade;not null" json:"grade"`
	ValidTime   *time.Time     `gorm:"column:valid_time" json:"valid_time"`
	IsForever   int8           `gorm:"column:is_forever;default:0" json:"is_forever"`
	MerID       uint           `gorm:"column:mer_id;default:0" json:"mer_id"`
	Status      int8           `gorm:"column:status;default:1" json:"status"`
	Mark        string         `gorm:"column:mark;size:255" json:"mark"`
	Remind      int8           `gorm:"column:remind;default:0" json:"remind"`
	IsDel       int8           `gorm:"column:is_del;default:0" json:"is_del"`
	CreatedAt   time.Time      `gorm:"column:add_time" json:"created_at"`
	UpdatedAt   time.Time      `gorm:"column:update_time" json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (UserLevel) TableName() string {
	return "user_level"
}

// UserSign 用户签到模型
type UserSign struct {
	ID        uint           `gorm:"primaryKey;column:id" json:"id"`
	UID       uint           `gorm:"column:uid;not null;index" json:"uid"`
	Title     string         `gorm:"column:title;size:255;not null" json:"title"`
	Number    int            `gorm:"column:number;not null" json:"number"`
	Balance   int            `gorm:"column:balance;not null" json:"balance"`
	CreatedAt time.Time      `gorm:"column:add_time" json:"created_at"`
	UpdatedAt time.Time      `gorm:"column:update_time" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (UserSign) TableName() string {
	return "user_sign"
}

// UserRelation 用户关联模型（收藏、关注等）
type UserRelation struct {
	UID      uint      `gorm:"primaryKey;column:uid" json:"uid"`
	TypeID   uint      `gorm:"primaryKey;column:type_id" json:"type_id"`
	Category string    `gorm:"primaryKey;column:category;size:32" json:"category"`
	Type     int8      `gorm:"column:type;not null" json:"type"`
	CreateAt time.Time `gorm:"column:create_time" json:"create_at"`
}

// TableName 指定表名
func (UserRelation) TableName() string {
	return "user_relation"
}

// UserToken 用户令牌模型（用于记录登录令牌）
type UserToken struct {
	ID           uint           `gorm:"primaryKey;column:id" json:"id"`
	UID          uint           `gorm:"column:uid;not null;index" json:"uid"`
	Token        string         `gorm:"column:token;size:512;not null;uniqueIndex" json:"token"`
	RefreshToken string         `gorm:"column:refresh_token;size:512" json:"refresh_token"`
	UserType     string         `gorm:"column:user_type;size:32;not null" json:"user_type"`
	ExpiresAt    time.Time      `gorm:"column:expires_at;not null" json:"expires_at"`
	CreatedAt    time.Time      `gorm:"column:created_at" json:"created_at"`
	UpdatedAt    time.Time      `gorm:"column:updated_at" json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (UserToken) TableName() string {
	return "user_token"
}

// IsExpired 检查令牌是否过期
func (ut *UserToken) IsExpired() bool {
	return time.Now().After(ut.ExpiresAt)
}

// UserLoginLog 用户登录日志模型
type UserLoginLog struct {
	ID        uint      `gorm:"primaryKey;column:id" json:"id"`
	UID       uint      `gorm:"column:uid;not null;index" json:"uid"`
	LoginIP   string    `gorm:"column:login_ip;size:16" json:"login_ip"`
	LoginType string    `gorm:"column:login_type;size:32" json:"login_type"`
	UserAgent string    `gorm:"column:user_agent;size:512" json:"user_agent"`
	CreatedAt time.Time `gorm:"column:created_at" json:"created_at"`
}

// TableName 指定表名
func (UserLoginLog) TableName() string {
	return "user_login_log"
}
