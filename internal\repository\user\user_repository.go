package user

import (
	"context"
	"errors"
	"time"

	"crmeb-go/internal/model"
	"crmeb-go/pkg/database"
	"gorm.io/gorm"
)

// UserRepository 用户仓储接口
type UserRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, user *model.User) error
	GetByID(ctx context.Context, uid uint) (*model.User, error)
	GetByAccount(ctx context.Context, account string) (*model.User, error)
	GetByPhone(ctx context.Context, phone string) (*model.User, error)
	Update(ctx context.Context, user *model.User) error
	Delete(ctx context.Context, uid uint) error
	
	// 查询操作
	List(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*model.User, int64, error)
	Exists(ctx context.Context, field, value string) (bool, error)
	Count(ctx context.Context, filters map[string]interface{}) (int64, error)
	
	// 业务操作
	UpdateLastLogin(ctx context.Context, uid uint, ip string) error
	UpdatePassword(ctx context.Context, uid uint, password string) error
	UpdateStatus(ctx context.Context, uid uint, status int8) error
	UpdateMoney(ctx context.Context, uid uint, amount float64) error
	UpdateIntegral(ctx context.Context, uid uint, integral int) error
	
	// 统计操作
	GetUserStats(ctx context.Context, uid uint) (*UserStats, error)
	GetTotalUsers(ctx context.Context) (int64, error)
	GetActiveUsers(ctx context.Context, days int) (int64, error)
}

// UserStats 用户统计信息
type UserStats struct {
	TotalOrders    int64   `json:"total_orders"`
	TotalSpent     float64 `json:"total_spent"`
	TotalIntegral  int     `json:"total_integral"`
	SpreadCount    int     `json:"spread_count"`
	LastLoginTime  time.Time `json:"last_login_time"`
}

// userRepository 用户仓储实现
type userRepository struct {
	db *gorm.DB
}

// NewUserRepository 创建用户仓储实例
func NewUserRepository() UserRepository {
	return &userRepository{
		db: database.GetDB(),
	}
}

// Create 创建用户
func (r *userRepository) Create(ctx context.Context, user *model.User) error {
	return r.db.WithContext(ctx).Create(user).Error
}

// GetByID 根据ID获取用户
func (r *userRepository) GetByID(ctx context.Context, uid uint) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).Where("uid = ? AND status != -1", uid).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// GetByAccount 根据账号获取用户
func (r *userRepository) GetByAccount(ctx context.Context, account string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).Where("account = ? AND status != -1", account).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// GetByPhone 根据手机号获取用户
func (r *userRepository) GetByPhone(ctx context.Context, phone string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).Where("phone = ? AND status != -1", phone).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// Update 更新用户
func (r *userRepository) Update(ctx context.Context, user *model.User) error {
	return r.db.WithContext(ctx).Save(user).Error
}

// Delete 删除用户（软删除）
func (r *userRepository) Delete(ctx context.Context, uid uint) error {
	return r.db.WithContext(ctx).Model(&model.User{}).Where("uid = ?", uid).Update("status", -1).Error
}

// List 获取用户列表
func (r *userRepository) List(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*model.User, int64, error) {
	var users []*model.User
	var total int64
	
	query := r.db.WithContext(ctx).Model(&model.User{}).Where("status != -1")
	
	// 应用过滤条件
	for key, value := range filters {
		switch key {
		case "status":
			query = query.Where("status = ?", value)
		case "user_type":
			query = query.Where("user_type = ?", value)
		case "is_promoter":
			query = query.Where("is_promoter = ?", value)
		case "keyword":
			query = query.Where("nickname LIKE ? OR account LIKE ? OR phone LIKE ?", 
				"%"+value.(string)+"%", "%"+value.(string)+"%", "%"+value.(string)+"%")
		case "start_time":
			query = query.Where("add_time >= ?", value)
		case "end_time":
			query = query.Where("add_time <= ?", value)
		}
	}
	
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("uid DESC").Find(&users).Error; err != nil {
		return nil, 0, err
	}
	
	return users, total, nil
}

// Exists 检查字段值是否存在
func (r *userRepository) Exists(ctx context.Context, field, value string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.User{}).Where(field+" = ? AND status != -1", value).Count(&count).Error
	return count > 0, err
}

// Count 统计用户数量
func (r *userRepository) Count(ctx context.Context, filters map[string]interface{}) (int64, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&model.User{}).Where("status != -1")
	
	// 应用过滤条件
	for key, value := range filters {
		switch key {
		case "status":
			query = query.Where("status = ?", value)
		case "user_type":
			query = query.Where("user_type = ?", value)
		case "is_promoter":
			query = query.Where("is_promoter = ?", value)
		}
	}
	
	err := query.Count(&count).Error
	return count, err
}

// UpdateLastLogin 更新最后登录信息
func (r *userRepository) UpdateLastLogin(ctx context.Context, uid uint, ip string) error {
	return r.db.WithContext(ctx).Model(&model.User{}).Where("uid = ?", uid).Updates(map[string]interface{}{
		"last_time": time.Now(),
		"last_ip":   ip,
	}).Error
}

// UpdatePassword 更新密码
func (r *userRepository) UpdatePassword(ctx context.Context, uid uint, password string) error {
	return r.db.WithContext(ctx).Model(&model.User{}).Where("uid = ?", uid).Update("pwd", password).Error
}

// UpdateStatus 更新状态
func (r *userRepository) UpdateStatus(ctx context.Context, uid uint, status int8) error {
	return r.db.WithContext(ctx).Model(&model.User{}).Where("uid = ?", uid).Update("status", status).Error
}

// UpdateMoney 更新余额
func (r *userRepository) UpdateMoney(ctx context.Context, uid uint, amount float64) error {
	return r.db.WithContext(ctx).Model(&model.User{}).Where("uid = ?", uid).Update("now_money", gorm.Expr("now_money + ?", amount)).Error
}

// UpdateIntegral 更新积分
func (r *userRepository) UpdateIntegral(ctx context.Context, uid uint, integral int) error {
	return r.db.WithContext(ctx).Model(&model.User{}).Where("uid = ?", uid).Update("integral", gorm.Expr("integral + ?", integral)).Error
}

// GetUserStats 获取用户统计信息
func (r *userRepository) GetUserStats(ctx context.Context, uid uint) (*UserStats, error) {
	var stats UserStats
	
	// 这里需要关联查询订单表等，暂时返回基础信息
	user, err := r.GetByID(ctx, uid)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, errors.New("user not found")
	}
	
	stats.LastLoginTime = user.LastTime
	stats.SpreadCount = user.SpreadCount
	stats.TotalIntegral = user.Integral
	
	return &stats, nil
}

// GetTotalUsers 获取用户总数
func (r *userRepository) GetTotalUsers(ctx context.Context) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.User{}).Where("status != -1").Count(&count).Error
	return count, err
}

// GetActiveUsers 获取活跃用户数（指定天数内登录过的用户）
func (r *userRepository) GetActiveUsers(ctx context.Context, days int) (int64, error) {
	var count int64
	since := time.Now().AddDate(0, 0, -days)
	err := r.db.WithContext(ctx).Model(&model.User{}).Where("status != -1 AND last_time >= ?", since).Count(&count).Error
	return count, err
}

// UserBillRepository 用户账单仓储接口
type UserBillRepository interface {
	Create(ctx context.Context, bill *model.UserBill) error
	GetByUserID(ctx context.Context, uid uint, page, pageSize int) ([]*model.UserBill, int64, error)
	GetByType(ctx context.Context, uid uint, billType string, page, pageSize int) ([]*model.UserBill, int64, error)
	GetUserBalance(ctx context.Context, uid uint, category string) (float64, error)
}

// userBillRepository 用户账单仓储实现
type userBillRepository struct {
	db *gorm.DB
}

// NewUserBillRepository 创建用户账单仓储实例
func NewUserBillRepository() UserBillRepository {
	return &userBillRepository{
		db: database.GetDB(),
	}
}

// Create 创建账单记录
func (r *userBillRepository) Create(ctx context.Context, bill *model.UserBill) error {
	return r.db.WithContext(ctx).Create(bill).Error
}

// GetByUserID 根据用户ID获取账单列表
func (r *userBillRepository) GetByUserID(ctx context.Context, uid uint, page, pageSize int) ([]*model.UserBill, int64, error) {
	var bills []*model.UserBill
	var total int64
	
	query := r.db.WithContext(ctx).Model(&model.UserBill{}).Where("uid = ?", uid)
	
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("id DESC").Find(&bills).Error; err != nil {
		return nil, 0, err
	}
	
	return bills, total, nil
}

// GetByType 根据类型获取账单列表
func (r *userBillRepository) GetByType(ctx context.Context, uid uint, billType string, page, pageSize int) ([]*model.UserBill, int64, error) {
	var bills []*model.UserBill
	var total int64
	
	query := r.db.WithContext(ctx).Model(&model.UserBill{}).Where("uid = ? AND type = ?", uid, billType)
	
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("id DESC").Find(&bills).Error; err != nil {
		return nil, 0, err
	}
	
	return bills, total, nil
}

// GetUserBalance 获取用户余额
func (r *userBillRepository) GetUserBalance(ctx context.Context, uid uint, category string) (float64, error) {
	var balance float64
	err := r.db.WithContext(ctx).Model(&model.UserBill{}).
		Where("uid = ? AND category = ? AND status = 1", uid, category).
		Select("COALESCE(SUM(CASE WHEN pm = 1 THEN number ELSE -number END), 0)").
		Scan(&balance).Error
	return balance, err
}
