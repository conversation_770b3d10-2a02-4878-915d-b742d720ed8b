package user

import (
	"context"
	"crypto/md5"
	"errors"
	"fmt"
	"time"

	"crmeb-go/internal/model"
	userRepo "crmeb-go/internal/repository/user"
	"crmeb-go/pkg/auth"
	"crmeb-go/pkg/cache"
	"crmeb-go/pkg/logger"
	"crmeb-go/pkg/sms"
	"crmeb-go/pkg/utils"

	"golang.org/x/crypto/bcrypt"
)

// UserService 用户服务接口
type UserService interface {
	// 认证相关
	Register(ctx context.Context, req *RegisterRequest) (*RegisterResponse, error)
	Login(ctx context.Context, req *LoginRequest) (*LoginResponse, error)
	SmsLogin(ctx context.Context, req *SmsLoginRequest) (*LoginResponse, error)
	SendSmsCode(ctx context.Context, req *SendSmsCodeRequest) error
	Logout(ctx context.Context, uid uint, token string) error
	RefreshToken(ctx context.Context, refreshToken string) (*TokenResponse, error)

	// 用户信息
	GetUserInfo(ctx context.Context, uid uint) (*UserInfoResponse, error)
	UpdateUserInfo(ctx context.Context, uid uint, req *UpdateUserInfoRequest) error
	ChangePassword(ctx context.Context, uid uint, req *ChangePasswordRequest) error

	// 用户管理
	GetUserList(ctx context.Context, req *GetUserListRequest) (*GetUserListResponse, error)
	CreateUser(ctx context.Context, req *CreateUserRequest) (*CreateUserResponse, error)
	UpdateUserStatus(ctx context.Context, uid uint, status int8) error
	DeleteUser(ctx context.Context, uid uint) error

	// 业务功能
	UpdateMoney(ctx context.Context, uid uint, amount float64, title, mark string) error
	UpdateIntegral(ctx context.Context, uid uint, integral int, title, mark string) error
	GetUserStats(ctx context.Context, uid uint) (*userRepo.UserStats, error)
}

// 请求和响应结构体
type RegisterRequest struct {
	Phone     string `json:"phone" binding:"required,len=11"`     // 手机号
	SmsCode   string `json:"sms_code" binding:"required,len=6"`   // 短信验证码
	Pwd       string `json:"pwd" binding:"required,min=6,max=32"` // 密码
	Spread    int    `json:"spread"`                              // 推广人ID
	AuthToken string `json:"auth_token"`                          // 微信授权token
	UserType  string `json:"user_type"`                           // 用户类型，默认h5
}

type RegisterResponse struct {
	Token       string      `json:"token"`        // JWT token字符串
	Exp         string      `json:"exp"`          // 过期时间描述，如"7200seconds"
	ExpiresTime int64       `json:"expires_time"` // 过期时间戳
	User        interface{} `json:"user"`         // 完整用户信息
}

type LoginRequest struct {
	Account   string `json:"account" binding:"required"`
	Password  string `json:"password" binding:"required"`
	AuthToken string `json:"auth_token"` // 微信授权token
	Spread    int    `json:"spread"`     // 推广人ID
	LoginIP   string `json:"-"`
}

type LoginResponse struct {
	Token       string      `json:"token"`        // JWT token字符串
	Exp         string      `json:"exp"`          // 过期时间描述，如"7200seconds"
	ExpiresTime int64       `json:"expires_time"` // 过期时间戳
	User        interface{} `json:"user"`         // 完整用户信息
}

type TokenResponse struct {
	Token *auth.TokenPair `json:"token"`
}

type UserInfoResponse struct {
	UID            uint       `json:"uid"`
	Account        string     `json:"account"`
	Nickname       string     `json:"nickname"`
	Avatar         string     `json:"avatar"`
	Phone          string     `json:"phone"`
	RealName       string     `json:"real_name"`
	Sex            int8       `json:"sex"`
	Age            int        `json:"age"`
	Birthday       *time.Time `json:"birthday"`
	NowMoney       float64    `json:"now_money"`
	BrokeragePrice float64    `json:"brokerage_price"`
	Integral       int        `json:"integral"`
	Level          int        `json:"level"`
	IsPromoter     int8       `json:"is_promoter"`
	PayCount       int        `json:"pay_count"`
	SpreadCount    int        `json:"spread_count"`
	Status         int8       `json:"status"`
	AddTime        time.Time  `json:"add_time"`
	LastTime       time.Time  `json:"last_time"`
}

type UpdateUserInfoRequest struct {
	Nickname string     `json:"nickname" binding:"max=60"`
	Avatar   string     `json:"avatar" binding:"max=256"`
	RealName string     `json:"real_name" binding:"max=25"`
	Sex      int8       `json:"sex" binding:"oneof=0 1 2"`
	Age      int        `json:"age" binding:"min=0,max=150"`
	Birthday *time.Time `json:"birthday"`
}

type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=6,max=32"`
}

type GetUserListRequest struct {
	Page     int                    `json:"page" binding:"min=1"`
	PageSize int                    `json:"page_size" binding:"min=1,max=100"`
	Filters  map[string]interface{} `json:"filters"`
}

type GetUserListResponse struct {
	List  []*UserInfoResponse `json:"list"`
	Total int64               `json:"total"`
	Page  int                 `json:"page"`
	Size  int                 `json:"size"`
}

type CreateUserRequest struct {
	Account  string `json:"account" binding:"required,min=3,max=32"`
	Password string `json:"password" binding:"required,min=6,max=32"`
	Phone    string `json:"phone" binding:"required,len=11"`
	Nickname string `json:"nickname" binding:"required,max=60"`
	Avatar   string `json:"avatar" binding:"max=256"`
	RealName string `json:"real_name" binding:"max=25"`
	Status   int8   `json:"status" binding:"oneof=0 1"`
}

type CreateUserResponse struct {
	UID      uint   `json:"uid"`
	Account  string `json:"account"`
	Nickname string `json:"nickname"`
}

// SmsLoginRequest 短信登录请求
type SmsLoginRequest struct {
	Phone    string `json:"phone" binding:"required,len=11"`
	SmsCode  string `json:"sms_code" binding:"required,len=4"`
	Spread   string `json:"spread,omitempty"`
	UserType string `json:"user_type,omitempty"`
	LoginIP  string `json:"-"`
}

// SendSmsCodeRequest 发送短信验证码请求
type SendSmsCodeRequest struct {
	Phone               string `json:"phone" binding:"required,len=11"`
	Type                string `json:"type" binding:"required,oneof=login register"`
	CaptchaType         string `json:"captchaType,omitempty"`
	CaptchaVerification string `json:"captchaVerification,omitempty"`
	Token               string `json:"token,omitempty"`
}

// userService 用户服务实现
type userService struct {
	userRepo     userRepo.UserRepository
	userBillRepo userRepo.UserBillRepository
	cache        cache.Cache
	jwtManager   *auth.JWTManager
}

// NewUserService 创建用户服务实例
func NewUserService(
	userRepo userRepo.UserRepository,
	userBillRepo userRepo.UserBillRepository,
	cache cache.Cache,
	jwtManager *auth.JWTManager,
) UserService {
	return &userService{
		userRepo:     userRepo,
		userBillRepo: userBillRepo,
		cache:        cache,
		jwtManager:   jwtManager,
	}
}

// Register 用户注册
func (s *userService) Register(ctx context.Context, req *RegisterRequest) (*RegisterResponse, error) {
	// 验证短信验证码
	if !s.verifySmsCode(ctx, req.Phone, req.SmsCode) {
		return nil, errors.New("验证码不正确")
	}

	// 检查手机号是否已存在
	exists, err := s.userRepo.Exists(ctx, "phone", req.Phone)
	if err != nil {
		logger.WithError(err).Error("检查手机号是否存在失败")
		return nil, errors.New("注册失败")
	}
	if exists {
		return nil, errors.New("用户已存在")
	}

	// 加密密码
	hashedPassword, err := s.hashPassword(req.Pwd)
	if err != nil {
		logger.WithError(err).Error("密码加密失败")
		return nil, errors.New("注册失败")
	}

	// 设置默认值
	userType := req.UserType
	if userType == "" {
		userType = "h5"
	}

	// 生成默认昵称
	nickname := s.generateDefaultNickname(req.Phone)

	// 创建用户
	user := &model.User{
		Account:  req.Phone, // 使用手机号作为账号
		Pwd:      hashedPassword,
		Phone:    req.Phone,
		Nickname: nickname,
		UserType: userType,
		Status:   1,
		AddIP:    utils.GetClientIP(ctx),
		LastIP:   utils.GetClientIP(ctx),
	}

	if err := s.userRepo.Create(ctx, user); err != nil {
		logger.WithError(err).Error("创建用户失败")
		return nil, errors.New("注册失败")
	}

	// 生成JWT令牌
	tokenPair, err := s.jwtManager.GenerateTokenPair(user.UID, user.Account, "user", 0)
	if err != nil {
		logger.WithError(err).Error("生成JWT令牌失败")
		return nil, errors.New("注册失败")
	}

	// 记录登录日志
	s.recordLoginLog(ctx, user.UID, utils.GetClientIP(ctx), "register")

	// 清除验证码
	s.clearCode(ctx, req.Phone)

	// 构建用户信息（与登录接口保持一致）
	userInfo := map[string]interface{}{
		"uid":             user.UID,
		"account":         user.Account,
		"phone":           user.Phone,
		"nickname":        user.Nickname,
		"avatar":          user.Avatar,
		"real_name":       user.RealName,
		"sex":             user.Sex,
		"age":             user.Age,
		"now_money":       user.NowMoney,
		"brokerage_price": user.BrokeragePrice,
		"integral":        user.Integral,
		"level":           user.Level,
		"is_promoter":     user.IsPromoter,
		"pay_count":       user.PayCount,
		"spread_count":    user.SpreadCount,
		"user_type":       user.UserType,
		"add_time":        user.AddTime,
	}

	// 计算过期时间（7200秒 = 2小时）
	expSeconds := int64(7200)
	expiresTime := time.Now().Unix() + expSeconds

	return &RegisterResponse{
		Token:       tokenPair.AccessToken,
		Exp:         fmt.Sprintf("%dseconds", expSeconds),
		ExpiresTime: expiresTime,
		User:        userInfo,
	}, nil
}

// Login 用户登录
func (s *userService) Login(ctx context.Context, req *LoginRequest) (*LoginResponse, error) {
	// 获取用户信息
	user, err := s.userRepo.GetByAccount(ctx, req.Account)
	if err != nil {
		logger.WithError(err).Error("获取用户信息失败")
		return nil, errors.New("登录失败")
	}
	if user == nil {
		return nil, errors.New("账号或密码错误")
	}

	// 检查用户状态
	if user.Status != 1 {
		return nil, errors.New("账号已被禁用")
	}

	// 验证密码
	if !s.verifyPassword(req.Password, user.Pwd) {
		return nil, errors.New("账号或密码错误")
	}

	// 更新最后登录信息
	if err := s.userRepo.UpdateLastLogin(ctx, user.UID, req.LoginIP); err != nil {
		logger.WithError(err).Error("更新最后登录信息失败")
	}

	// 生成JWT令牌
	tokenPair, err := s.jwtManager.GenerateTokenPair(user.UID, user.Account, "user", 0)
	if err != nil {
		logger.WithError(err).Error("生成JWT令牌失败")
		return nil, errors.New("登录失败")
	}

	// 记录登录日志
	s.recordLoginLog(ctx, user.UID, req.LoginIP, "login")

	// 构建用户信息（隐藏敏感字段，与PHP版本一致）
	userInfo := map[string]interface{}{
		"uid":             user.UID,
		"account":         user.Account,
		"phone":           user.Phone,
		"nickname":        user.Nickname,
		"avatar":          user.Avatar,
		"real_name":       user.RealName,
		"sex":             user.Sex,
		"age":             user.Age,
		"now_money":       user.NowMoney,
		"brokerage_price": user.BrokeragePrice,
		"integral":        user.Integral,
		"level":           user.Level,
		"is_promoter":     user.IsPromoter,
		"pay_count":       user.PayCount,
		"spread_count":    user.SpreadCount,
		"user_type":       user.UserType,
		"add_time":        user.AddTime,
		// 隐藏敏感字段：pwd, last_time, last_ip, create_time, mark, status, spread_uid, spread_time, birthday
	}

	// 计算过期时间（7200秒 = 2小时）
	expSeconds := int64(7200)
	expiresTime := time.Now().Unix() + expSeconds

	return &LoginResponse{
		Token:       tokenPair.AccessToken,
		Exp:         fmt.Sprintf("%dseconds", expSeconds),
		ExpiresTime: expiresTime,
		User:        userInfo,
	}, nil
}

// Logout 用户登出
func (s *userService) Logout(ctx context.Context, uid uint, token string) error {
	// 将令牌加入黑名单
	if err := s.jwtManager.BlacklistToken(token); err != nil {
		logger.WithError(err).Error("将令牌加入黑名单失败")
		return errors.New("登出失败")
	}

	// 记录登录日志
	s.recordLoginLog(ctx, uid, utils.GetClientIP(ctx), "logout")

	return nil
}

// RefreshToken 刷新令牌
func (s *userService) RefreshToken(ctx context.Context, refreshToken string) (*TokenResponse, error) {
	tokenPair, err := s.jwtManager.RefreshToken(refreshToken)
	if err != nil {
		return nil, errors.New("刷新令牌失败: " + err.Error())
	}

	return &TokenResponse{
		Token: tokenPair,
	}, nil
}

// GetUserInfo 获取用户信息
func (s *userService) GetUserInfo(ctx context.Context, uid uint) (*UserInfoResponse, error) {
	user, err := s.userRepo.GetByID(ctx, uid)
	if err != nil {
		logger.WithError(err).Error("获取用户信息失败")
		return nil, errors.New("获取用户信息失败")
	}
	if user == nil {
		return nil, errors.New("用户不存在")
	}

	return s.convertToUserInfoResponse(user), nil
}

// 辅助方法
func (s *userService) hashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return string(bytes), err
}

func (s *userService) verifyPassword(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

func (s *userService) generateDefaultNickname(phone string) string {
	if len(phone) >= 11 {
		return phone[:3] + "****" + phone[7:]
	}
	return "用户" + fmt.Sprintf("%x", md5.Sum([]byte(phone)))[:8]
}

func (s *userService) verifyCode(ctx context.Context, phone, code string) bool {
	// 从缓存中获取验证码
	key := cache.BuildCacheKey("sms_code:", phone)
	cachedCode, err := s.cache.Get(ctx, key)
	if err != nil {
		return false
	}
	return cachedCode == code
}

func (s *userService) verifySmsCode(ctx context.Context, phone, code string) bool {
	// 验证短信验证码（与PHP版本SmsService::checkSmsCode一致）
	key := cache.BuildCacheKey("sms_code_login:", phone)
	cachedCode, err := s.cache.Get(ctx, key)
	if err != nil {
		logger.WithError(err).Error("获取短信验证码失败")
		return false
	}
	if cachedCode != code {
		return false
	}
	// 验证成功后删除验证码
	s.cache.Delete(ctx, key)
	return true
}

func (s *userService) clearCode(ctx context.Context, phone string) {
	key := cache.BuildCacheKey("sms_code:", phone)
	s.cache.Delete(ctx, key)
}

func (s *userService) recordLoginLog(ctx context.Context, uid uint, ip, loginType string) {
	// 这里可以实现登录日志记录
	logger.WithFields(map[string]interface{}{
		"uid":        uid,
		"ip":         ip,
		"login_type": loginType,
	}).Info("用户登录日志")
}

func (s *userService) convertToUserInfoResponse(user *model.User) *UserInfoResponse {
	return &UserInfoResponse{
		UID:            user.UID,
		Account:        user.Account,
		Nickname:       user.Nickname,
		Avatar:         user.Avatar,
		Phone:          user.Phone,
		RealName:       user.RealName,
		Sex:            user.Sex,
		Age:            user.Age,
		Birthday:       user.Birthday,
		NowMoney:       user.NowMoney,
		BrokeragePrice: user.BrokeragePrice,
		Integral:       user.Integral,
		Level:          user.Level,
		IsPromoter:     user.IsPromoter,
		PayCount:       user.PayCount,
		SpreadCount:    user.SpreadCount,
		Status:         user.Status,
		AddTime:        user.AddTime,
		LastTime:       user.LastTime,
	}
}

// UpdateUserInfo 更新用户信息
func (s *userService) UpdateUserInfo(ctx context.Context, uid uint, req *UpdateUserInfoRequest) error {
	user, err := s.userRepo.GetByID(ctx, uid)
	if err != nil {
		return err
	}
	if user == nil {
		return errors.New("用户不存在")
	}

	// 更新字段
	if req.Nickname != "" {
		user.Nickname = req.Nickname
	}
	if req.Avatar != "" {
		user.Avatar = req.Avatar
	}
	if req.RealName != "" {
		user.RealName = req.RealName
	}
	user.Sex = req.Sex
	user.Age = req.Age
	if req.Birthday != nil {
		user.Birthday = req.Birthday
	}

	return s.userRepo.Update(ctx, user)
}

// ChangePassword 修改密码
func (s *userService) ChangePassword(ctx context.Context, uid uint, req *ChangePasswordRequest) error {
	user, err := s.userRepo.GetByID(ctx, uid)
	if err != nil {
		return err
	}
	if user == nil {
		return errors.New("用户不存在")
	}

	// 验证旧密码
	if !s.verifyPassword(req.OldPassword, user.Pwd) {
		return errors.New("原密码错误")
	}

	// 加密新密码
	hashedPassword, err := s.hashPassword(req.NewPassword)
	if err != nil {
		return err
	}

	return s.userRepo.UpdatePassword(ctx, uid, hashedPassword)
}

// GetUserList 获取用户列表
func (s *userService) GetUserList(ctx context.Context, req *GetUserListRequest) (*GetUserListResponse, error) {
	users, total, err := s.userRepo.List(ctx, req.Page, req.PageSize, req.Filters)
	if err != nil {
		return nil, err
	}

	var userList []*UserInfoResponse
	for _, user := range users {
		userList = append(userList, s.convertToUserInfoResponse(user))
	}

	return &GetUserListResponse{
		List:  userList,
		Total: total,
		Page:  req.Page,
		Size:  req.PageSize,
	}, nil
}

// CreateUser 创建用户
func (s *userService) CreateUser(ctx context.Context, req *CreateUserRequest) (*CreateUserResponse, error) {
	// 检查账号是否已存在
	exists, err := s.userRepo.Exists(ctx, "account", req.Account)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, errors.New("账号已存在")
	}

	// 检查手机号是否已存在
	exists, err = s.userRepo.Exists(ctx, "phone", req.Phone)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, errors.New("手机号已被注册")
	}

	// 加密密码
	hashedPassword, err := s.hashPassword(req.Password)
	if err != nil {
		return nil, err
	}

	// 创建用户
	user := &model.User{
		Account:  req.Account,
		Pwd:      hashedPassword,
		Phone:    req.Phone,
		Nickname: req.Nickname,
		Avatar:   req.Avatar,
		RealName: req.RealName,
		UserType: "h5",
		Status:   req.Status,
	}

	if err := s.userRepo.Create(ctx, user); err != nil {
		return nil, err
	}

	return &CreateUserResponse{
		UID:      user.UID,
		Account:  user.Account,
		Nickname: user.Nickname,
	}, nil
}

// UpdateUserStatus 更新用户状态
func (s *userService) UpdateUserStatus(ctx context.Context, uid uint, status int8) error {
	return s.userRepo.UpdateStatus(ctx, uid, status)
}

// DeleteUser 删除用户
func (s *userService) DeleteUser(ctx context.Context, uid uint) error {
	return s.userRepo.Delete(ctx, uid)
}

// UpdateMoney 更新用户余额
func (s *userService) UpdateMoney(ctx context.Context, uid uint, amount float64, title, mark string) error {
	// 在事务中处理
	return s.userRepo.UpdateMoney(ctx, uid, amount)
}

// UpdateIntegral 更新用户积分
func (s *userService) UpdateIntegral(ctx context.Context, uid uint, integral int, title, mark string) error {
	return s.userRepo.UpdateIntegral(ctx, uid, integral)
}

// GetUserStats 获取用户统计
func (s *userService) GetUserStats(ctx context.Context, uid uint) (*userRepo.UserStats, error) {
	return s.userRepo.GetUserStats(ctx, uid)
}

// SmsLogin 短信登录
func (s *userService) SmsLogin(ctx context.Context, req *SmsLoginRequest) (*LoginResponse, error) {
	// 验证短信验证码
	smsService := sms.GetGlobalSmsService()
	if smsService == nil {
		return nil, errors.New("短信服务未初始化")
	}

	err := smsService.VerifyCode(ctx, req.Phone, req.SmsCode, "login")
	if err != nil {
		return nil, err
	}

	// 查找用户，如果不存在则自动创建
	user, err := s.userRepo.GetByPhone(ctx, req.Phone)
	if err != nil {
		logger.WithError(err).Error("查询用户失败")
		return nil, errors.New("登录失败")
	}

	// 如果用户不存在，自动创建用户（与PHP版本逻辑一致）
	if user == nil {
		userType := req.UserType
		if userType == "" {
			userType = "h5"
		}

		// 生成默认昵称
		nickname := s.generateDefaultNickname(req.Phone)

		// 创建新用户
		user = &model.User{
			Account:  req.Phone, // 使用手机号作为账号
			Phone:    req.Phone,
			Nickname: nickname,
			UserType: userType,
			Status:   1,
			AddIP:    req.LoginIP,
			LastIP:   req.LoginIP,
		}

		if err := s.userRepo.Create(ctx, user); err != nil {
			logger.WithError(err).Error("创建用户失败")
			return nil, errors.New("登录失败")
		}
	} else {
		// 检查用户状态
		if user.Status != 1 {
			return nil, errors.New("账号已被禁用")
		}

		// 更新最后登录信息
		if err := s.userRepo.UpdateLastLogin(ctx, user.UID, req.LoginIP); err != nil {
			logger.WithError(err).Error("更新最后登录信息失败")
		}
	}

	// 生成JWT令牌
	tokenPair, err := s.jwtManager.GenerateTokenPair(user.UID, user.Account, "user", 0)
	if err != nil {
		logger.WithError(err).Error("生成JWT令牌失败")
		return nil, errors.New("登录失败")
	}

	// 记录登录日志
	s.recordLoginLog(ctx, user.UID, req.LoginIP, "sms_login")

	// 构建用户信息（与登录接口保持一致）
	userInfo := map[string]interface{}{
		"uid":             user.UID,
		"account":         user.Account,
		"phone":           user.Phone,
		"nickname":        user.Nickname,
		"avatar":          user.Avatar,
		"real_name":       user.RealName,
		"sex":             user.Sex,
		"age":             user.Age,
		"now_money":       user.NowMoney,
		"brokerage_price": user.BrokeragePrice,
		"integral":        user.Integral,
		"level":           user.Level,
		"is_promoter":     user.IsPromoter,
		"pay_count":       user.PayCount,
		"spread_count":    user.SpreadCount,
		"user_type":       user.UserType,
		"add_time":        user.AddTime,
	}

	// 计算过期时间（7200秒 = 2小时）
	expSeconds := int64(7200)
	expiresTime := time.Now().Unix() + expSeconds

	return &LoginResponse{
		Token:       tokenPair.AccessToken,
		Exp:         fmt.Sprintf("%dseconds", expSeconds),
		ExpiresTime: expiresTime,
		User:        userInfo,
	}, nil
}

// SendSmsCode 发送短信验证码
func (s *userService) SendSmsCode(ctx context.Context, req *SendSmsCodeRequest) error {
	// 获取短信服务实例
	smsService := sms.GetGlobalSmsService()
	if smsService == nil {
		return errors.New("短信服务未初始化")
	}

	// 发送验证码
	return smsService.SendCode(ctx, req.Phone, req.Type)
}
