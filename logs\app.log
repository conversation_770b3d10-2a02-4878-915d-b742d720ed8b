{"level":"info","msg":"<PERSON>gger initialized successfully","time":"2025-07-29 22:16:45"}
{"level":"fatal","msg":"Failed to initialize database: failed to connect to database: dial tcp [::1]:3306: connectex: No connection could be made because the target machine actively refused it.","time":"2025-07-29 22:16:45"}
{"level":"info","msg":"Logger initialized successfully","time":"2025-07-29 22:22:54"}
{"level":"fatal","msg":"Failed to initialize database: failed to connect to database: driver: bad connection","time":"2025-07-29 22:23:09"}
{"level":"info","msg":"Logger initialized successfully","time":"2025-07-29 22:23:45"}
{"level":"fatal","msg":"Failed to initialize database: failed to connect to database: driver: bad connection","time":"2025-07-29 22:24:00"}
{"level":"info","msg":"Logger initialized successfully","time":"2025-07-29 22:24:40"}
{"level":"fatal","msg":"Failed to initialize database: failed to connect to database: dial tcp ************:3306: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.","time":"2025-07-29 22:25:01"}
{"level":"info","msg":"Logger initialized successfully","time":"2025-07-29 22:25:30"}
{"level":"fatal","msg":"Failed to initialize database: failed to connect to database: dial tcp ************:3306: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.","time":"2025-07-29 22:25:51"}
{"level":"info","msg":"Logger initialized successfully","time":"2025-07-29 22:28:02"}
{"level":"fatal","msg":"Failed to initialize database: failed to connect to database: dial tcp ************:3306: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.","time":"2025-07-29 22:28:23"}
{"level":"info","msg":"Logger initialized successfully","time":"2025-07-30 00:04:29"}
{"level":"info","msg":"Database initialized successfully","time":"2025-07-30 00:04:30"}
{"level":"fatal","msg":"Failed to initialize Redis: failed to connect to redis: ERR illegal address: **************:8173","time":"2025-07-30 00:04:30"}
{"level":"info","msg":"Logger initialized successfully","time":"2025-07-30 00:04:40"}
{"level":"info","msg":"Database initialized successfully","time":"2025-07-30 00:04:41"}
{"level":"fatal","msg":"Failed to initialize Redis: failed to connect to redis: ERR illegal address: **************:20948","time":"2025-07-30 00:04:41"}
{"level":"info","msg":"Logger initialized successfully","time":"2025-07-30 00:05:01"}
{"level":"info","msg":"Database initialized successfully","time":"2025-07-30 00:05:01"}
{"level":"fatal","msg":"Failed to initialize Redis: failed to connect to redis: ERR illegal address: **************:20681","time":"2025-07-30 00:05:01"}
{"level":"info","msg":"Logger initialized successfully","time":"2025-07-30 00:18:51"}
{"level":"info","msg":"Database initialized successfully","time":"2025-07-30 00:18:52"}
{"level":"info","msg":"Redis initialized successfully","time":"2025-07-30 00:18:52"}
{"level":"info","msg":"JWT manager initialized successfully","time":"2025-07-30 00:18:52"}
{"level":"info","msg":"Dependency injection container initialized successfully","time":"2025-07-30 00:18:52"}
{"level":"info","msg":"Routes setup completed","time":"2025-07-30 00:18:52"}
{"level":"info","msg":"Starting server on port 8080","time":"2025-07-30 00:18:52"}
{"client_ip":"::1","level":"info","method":"GET","msg":"Request started","path":"/health","query_params":"","request_id":"5a105c1b-ee2b-4cf2-a2ba-c1637fad97c2","time":"2025-07-30 00:20:02","timestamp":"2025-07-30 00:20:02","user_agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","user_id":""}
{"client_ip":"::1","duration":"0s","level":"info","method":"GET","msg":"Request completed","path":"/health","request_id":"5a105c1b-ee2b-4cf2-a2ba-c1637fad97c2","status_code":200,"time":"2025-07-30 00:20:02","timestamp":"2025-07-30 00:20:02","user_id":""}
{"client_ip":"::1","level":"info","method":"GET","msg":"Request started","path":"/swagger/index.html","query_params":"","request_id":"8b385d21-95e4-4b01-8e44-16621f41c18a","time":"2025-07-30 00:22:16","timestamp":"2025-07-30 00:22:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","user_id":""}
{"client_ip":"::1","duration":"505.6µs","level":"info","method":"GET","msg":"Request completed","path":"/swagger/index.html","request_id":"8b385d21-95e4-4b01-8e44-16621f41c18a","status_code":200,"time":"2025-07-30 00:22:16","timestamp":"2025-07-30 00:22:16","user_id":""}
{"client_ip":"::1","level":"info","method":"GET","msg":"Request started","path":"/swagger/swagger-ui.css","query_params":"","request_id":"de49051c-7d54-4833-880e-ae9cbf0dc556","time":"2025-07-30 00:22:16","timestamp":"2025-07-30 00:22:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","user_id":""}
{"client_ip":"::1","level":"info","method":"GET","msg":"Request started","path":"/swagger/swagger-ui-bundle.js","query_params":"","request_id":"3bff1384-fe6d-4302-b82b-00c9c243f407","time":"2025-07-30 00:22:16","timestamp":"2025-07-30 00:22:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","user_id":""}
{"client_ip":"::1","level":"info","method":"GET","msg":"Request started","path":"/swagger/swagger-ui-standalone-preset.js","query_params":"","request_id":"460e5902-3a65-4969-ac47-2fbc43114506","time":"2025-07-30 00:22:16","timestamp":"2025-07-30 00:22:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","user_id":""}
{"client_ip":"::1","duration":"0s","level":"info","method":"GET","msg":"Request completed","path":"/swagger/swagger-ui.css","request_id":"de49051c-7d54-4833-880e-ae9cbf0dc556","status_code":200,"time":"2025-07-30 00:22:16","timestamp":"2025-07-30 00:22:16","user_id":""}
{"client_ip":"::1","duration":"504.5µs","level":"info","method":"GET","msg":"Request completed","path":"/swagger/swagger-ui-standalone-preset.js","request_id":"460e5902-3a65-4969-ac47-2fbc43114506","status_code":200,"time":"2025-07-30 00:22:16","timestamp":"2025-07-30 00:22:16","user_id":""}
{"client_ip":"::1","duration":"24.8212ms","level":"info","method":"GET","msg":"Request completed","path":"/swagger/swagger-ui-bundle.js","request_id":"3bff1384-fe6d-4302-b82b-00c9c243f407","status_code":200,"time":"2025-07-30 00:22:16","timestamp":"2025-07-30 00:22:16","user_id":""}
{"client_ip":"::1","level":"info","method":"GET","msg":"Request started","path":"/swagger/doc.json","query_params":"","request_id":"1164310f-5e03-436d-b9dd-a4203b9dfec9","time":"2025-07-30 00:22:16","timestamp":"2025-07-30 00:22:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","user_id":""}
{"client_ip":"::1","duration":"0s","level":"info","method":"GET","msg":"Request completed","path":"/swagger/doc.json","request_id":"1164310f-5e03-436d-b9dd-a4203b9dfec9","status_code":500,"time":"2025-07-30 00:22:16","timestamp":"2025-07-30 00:22:16","user_id":""}
{"client_ip":"::1","level":"info","method":"GET","msg":"Request started","path":"/swagger/favicon-32x32.png","query_params":"","request_id":"857021ff-0400-466e-a503-1391b26deea4","time":"2025-07-30 00:22:16","timestamp":"2025-07-30 00:22:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","user_id":""}
{"client_ip":"::1","duration":"0s","level":"info","method":"GET","msg":"Request completed","path":"/swagger/favicon-32x32.png","request_id":"857021ff-0400-466e-a503-1391b26deea4","status_code":200,"time":"2025-07-30 00:22:16","timestamp":"2025-07-30 00:22:16","user_id":""}
{"client_ip":"::1","level":"info","method":"GET","msg":"Request started","path":"/swagger/index.html","query_params":"","request_id":"193060cc-3fbf-4ff2-a22f-9b3b3cbfa397","time":"2025-07-30 00:22:30","timestamp":"2025-07-30 00:22:30","user_agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","user_id":""}
{"client_ip":"::1","duration":"506.4µs","level":"info","method":"GET","msg":"Request completed","path":"/swagger/index.html","request_id":"193060cc-3fbf-4ff2-a22f-9b3b3cbfa397","status_code":200,"time":"2025-07-30 00:22:30","timestamp":"2025-07-30 00:22:30","user_id":""}
{"level":"info","msg":"Shutting down server...","time":"2025-07-30 00:27:46"}
{"level":"info","msg":"Server exited","time":"2025-07-30 00:27:46"}
{"level":"info","msg":"Logger initialized successfully","time":"2025-07-30 00:28:00"}
{"level":"info","msg":"Database initialized successfully","time":"2025-07-30 00:28:01"}
{"level":"info","msg":"Redis initialized successfully","time":"2025-07-30 00:28:01"}
{"level":"info","msg":"JWT manager initialized successfully","time":"2025-07-30 00:28:01"}
{"level":"info","msg":"Dependency injection container initialized successfully","time":"2025-07-30 00:28:01"}
{"level":"info","msg":"Routes setup completed","time":"2025-07-30 00:28:01"}
{"level":"info","msg":"Starting server on port 8080","time":"2025-07-30 00:28:01"}
{"client_ip":"::1","level":"info","method":"GET","msg":"Request started","path":"/swagger/doc.json","query_params":"","request_id":"61c00696-6288-40e5-8c79-b5e752de3767","time":"2025-07-30 00:28:35","timestamp":"2025-07-30 00:28:35","user_agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","user_id":""}
{"client_ip":"::1","duration":"505.6µs","level":"info","method":"GET","msg":"Request completed","path":"/swagger/doc.json","request_id":"61c00696-6288-40e5-8c79-b5e752de3767","status_code":200,"time":"2025-07-30 00:28:35","timestamp":"2025-07-30 00:28:35","user_id":""}
{"level":"info","msg":"Logger initialized successfully","time":"2025-07-30 00:38:50"}
{"level":"info","msg":"Database initialized successfully","time":"2025-07-30 00:38:51"}
{"level":"info","msg":"Redis initialized successfully","time":"2025-07-30 00:38:51"}
{"level":"info","msg":"JWT manager initialized successfully","time":"2025-07-30 00:38:51"}
{"level":"info","msg":"Dependency injection container initialized successfully","time":"2025-07-30 00:38:51"}
{"level":"info","msg":"Routes setup completed","time":"2025-07-30 00:38:51"}
{"level":"info","msg":"Starting server on port 8080","time":"2025-07-30 00:38:51"}
{"level":"fatal","msg":"Failed to start server: listen tcp :8080: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.","time":"2025-07-30 00:38:51"}
{"level":"info","msg":"Logger initialized successfully","time":"2025-07-30 00:40:19"}
{"level":"info","msg":"Database initialized successfully","time":"2025-07-30 00:40:19"}
{"level":"info","msg":"Redis initialized successfully","time":"2025-07-30 00:40:20"}
{"level":"info","msg":"JWT manager initialized successfully","time":"2025-07-30 00:40:20"}
{"level":"info","msg":"Dependency injection container initialized successfully","time":"2025-07-30 00:40:20"}
{"level":"info","msg":"Routes setup completed","time":"2025-07-30 00:40:20"}
{"level":"info","msg":"Starting server on port 8080","time":"2025-07-30 00:40:20"}
{"client_ip":"::1","level":"info","method":"GET","msg":"Request started","path":"/swagger/index.html","query_params":"","request_id":"8f86e725-4671-481b-9628-1d9f79cc5680","time":"2025-07-30 00:40:37","timestamp":"2025-07-30 00:40:37","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","user_id":""}
{"client_ip":"::1","duration":"502µs","level":"info","method":"GET","msg":"Request completed","path":"/swagger/index.html","request_id":"8f86e725-4671-481b-9628-1d9f79cc5680","status_code":200,"time":"2025-07-30 00:40:37","timestamp":"2025-07-30 00:40:37","user_id":""}
{"client_ip":"::1","level":"info","method":"GET","msg":"Request started","path":"/swagger/swagger-ui.css","query_params":"","request_id":"feeb28b8-12fc-4ae0-b3ad-255ef44c7157","time":"2025-07-30 00:40:37","timestamp":"2025-07-30 00:40:37","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","user_id":""}
{"client_ip":"::1","duration":"0s","level":"info","method":"GET","msg":"Request completed","path":"/swagger/swagger-ui.css","request_id":"feeb28b8-12fc-4ae0-b3ad-255ef44c7157","status_code":200,"time":"2025-07-30 00:40:37","timestamp":"2025-07-30 00:40:37","user_id":""}
{"client_ip":"::1","level":"info","method":"GET","msg":"Request started","path":"/swagger/swagger-ui-bundle.js","query_params":"","request_id":"818be263-e0e8-44b9-bf21-f0187e58393a","time":"2025-07-30 00:40:37","timestamp":"2025-07-30 00:40:37","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","user_id":""}
{"client_ip":"::1","level":"info","method":"GET","msg":"Request started","path":"/swagger/swagger-ui-standalone-preset.js","query_params":"","request_id":"b265f0c5-5d38-43a3-acb9-742f676e540d","time":"2025-07-30 00:40:37","timestamp":"2025-07-30 00:40:37","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","user_id":""}
{"client_ip":"::1","duration":"507.8µs","level":"info","method":"GET","msg":"Request completed","path":"/swagger/swagger-ui-standalone-preset.js","request_id":"b265f0c5-5d38-43a3-acb9-742f676e540d","status_code":200,"time":"2025-07-30 00:40:37","timestamp":"2025-07-30 00:40:37","user_id":""}
{"client_ip":"::1","duration":"3.2172ms","level":"info","method":"GET","msg":"Request completed","path":"/swagger/swagger-ui-bundle.js","request_id":"818be263-e0e8-44b9-bf21-f0187e58393a","status_code":200,"time":"2025-07-30 00:40:37","timestamp":"2025-07-30 00:40:37","user_id":""}
{"client_ip":"::1","level":"info","method":"GET","msg":"Request started","path":"/swagger/doc.json","query_params":"","request_id":"5239e611-a37e-4323-9967-7cb42dcce65d","time":"2025-07-30 00:40:38","timestamp":"2025-07-30 00:40:38","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","user_id":""}
{"client_ip":"::1","duration":"0s","level":"info","method":"GET","msg":"Request completed","path":"/swagger/doc.json","request_id":"5239e611-a37e-4323-9967-7cb42dcce65d","status_code":200,"time":"2025-07-30 00:40:38","timestamp":"2025-07-30 00:40:38","user_id":""}
{"client_ip":"::1","level":"info","method":"GET","msg":"Request started","path":"/swagger/favicon-32x32.png","query_params":"","request_id":"975bcdb6-694a-46ce-91f2-b40f7e1f9662","time":"2025-07-30 00:40:38","timestamp":"2025-07-30 00:40:38","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","user_id":""}
{"client_ip":"::1","duration":"0s","level":"info","method":"GET","msg":"Request completed","path":"/swagger/favicon-32x32.png","request_id":"975bcdb6-694a-46ce-91f2-b40f7e1f9662","status_code":200,"time":"2025-07-30 00:40:38","timestamp":"2025-07-30 00:40:38","user_id":""}
{"client_ip":"::1","level":"info","method":"GET","msg":"Request started","path":"/api/v1/admin/users","query_params":"page=1\u0026page_size=10","request_id":"4e892ce6-0c68-4a7f-88bf-5a3e56bdb039","time":"2025-07-30 00:40:45","timestamp":"2025-07-30 00:40:45","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","user_id":""}
{"client_ip":"::1","duration":"0s","level":"info","method":"GET","msg":"Request completed","path":"/api/v1/admin/users","request_id":"4e892ce6-0c68-4a7f-88bf-5a3e56bdb039","status_code":401,"time":"2025-07-30 00:40:45","timestamp":"2025-07-30 00:40:45","user_id":""}
{"client_ip":"::1","level":"info","method":"POST","msg":"Request started","path":"/api/v1/auth/login","query_params":"","request_id":"c97f5b33-ea6d-440d-8019-77879c28f187","time":"2025-07-30 00:41:01","timestamp":"2025-07-30 00:41:01","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","user_id":""}
{"error":"Error 1146 (42S02): Table 'crmeb.user' doesn't exist","level":"error","msg":"获取用户信息失败","time":"2025-07-30 00:41:01"}
{"client_ip":"::1","duration":"61.7813ms","level":"info","method":"POST","msg":"Request completed","path":"/api/v1/auth/login","request_id":"c97f5b33-ea6d-440d-8019-77879c28f187","status_code":401,"time":"2025-07-30 00:41:01","timestamp":"2025-07-30 00:41:01","user_id":""}
{"client_ip":"::1","level":"info","method":"GET","msg":"Request started","path":"/health","query_params":"","request_id":"02abd485-ddca-43cf-9ff8-0581e555c89e","time":"2025-07-30 00:41:09","timestamp":"2025-07-30 00:41:09","user_agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","user_id":""}
{"client_ip":"::1","duration":"0s","level":"info","method":"GET","msg":"Request completed","path":"/health","request_id":"02abd485-ddca-43cf-9ff8-0581e555c89e","status_code":200,"time":"2025-07-30 00:41:09","timestamp":"2025-07-30 00:41:09","user_id":""}
{"client_ip":"::1","level":"info","method":"GET","msg":"Request started","path":"/swagger/doc.json","query_params":"","request_id":"9c23d067-c3e5-443f-9671-45fd1f1efcf2","time":"2025-07-30 00:41:41","timestamp":"2025-07-30 00:41:41","user_agent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652","user_id":""}
{"client_ip":"::1","duration":"502.2µs","level":"info","method":"GET","msg":"Request completed","path":"/swagger/doc.json","request_id":"9c23d067-c3e5-443f-9671-45fd1f1efcf2","status_code":200,"time":"2025-07-30 00:41:41","timestamp":"2025-07-30 00:41:41","user_id":""}
{"level":"info","msg":"Shutting down server...","time":"2025-07-30 01:00:35"}
{"level":"error","msg":"Failed to close database: bad connection","time":"2025-07-30 01:00:35"}
{"level":"info","msg":"Server exited","time":"2025-07-30 01:00:35"}
{"level":"info","msg":"Logger initialized successfully","time":"2025-07-30 01:02:10"}
{"level":"info","msg":"Database initialized successfully","time":"2025-07-30 01:02:10"}
{"level":"info","msg":"Redis initialized successfully","time":"2025-07-30 01:02:10"}
{"level":"info","msg":"JWT manager initialized successfully","time":"2025-07-30 01:02:10"}
{"level":"info","msg":"Dependency injection container initialized successfully","time":"2025-07-30 01:02:10"}
{"level":"info","msg":"Routes setup completed","time":"2025-07-30 01:02:10"}
{"level":"info","msg":"Starting server on port 8080","time":"2025-07-30 01:02:10"}
{"level":"info","msg":"Shutting down server...","time":"2025-07-30 01:03:35"}
{"level":"info","msg":"Server exited","time":"2025-07-30 01:03:35"}
