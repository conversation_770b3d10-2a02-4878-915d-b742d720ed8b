package auth

import (
	"errors"
	"time"

	"crmeb-go/internal/config"
	"github.com/golang-jwt/jwt/v5"
)

// Claims JWT声明结构
type Claims struct {
	UserID   uint   `json:"user_id"`
	Username string `json:"username"`
	UserType string `json:"user_type"` // user, admin, merchant
	MerchantID uint `json:"merchant_id,omitempty"`
	jwt.RegisteredClaims
}

// TokenPair 令牌对
type TokenPair struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int64  `json:"expires_in"`
}

// JWTManager JWT管理器
type JWTManager struct {
	secretKey     []byte
	expire        time.Duration
	refreshExpire time.Duration
	issuer        string
}

// NewJWTManager 创建JWT管理器
func NewJWTManager(cfg *config.JWTConfig) *JWTManager {
	return &JWTManager{
		secretKey:     []byte(cfg.Secret),
		expire:        cfg.Expire,
		refreshExpire: cfg.RefreshExpire,
		issuer:        cfg.Issuer,
	}
}

// GenerateToken 生成访问令牌
func (j *JWTManager) GenerateToken(userID uint, username, userType string, merchantID uint) (string, error) {
	now := time.Now()
	claims := Claims{
		UserID:     userID,
		Username:   username,
		UserType:   userType,
		MerchantID: merchantID,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    j.issuer,
			Subject:   username,
			IssuedAt:  jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(now.Add(j.expire)),
			NotBefore: jwt.NewNumericDate(now),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(j.secretKey)
}

// GenerateRefreshToken 生成刷新令牌
func (j *JWTManager) GenerateRefreshToken(userID uint, username, userType string, merchantID uint) (string, error) {
	now := time.Now()
	claims := Claims{
		UserID:     userID,
		Username:   username,
		UserType:   userType,
		MerchantID: merchantID,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    j.issuer,
			Subject:   username,
			IssuedAt:  jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(now.Add(j.refreshExpire)),
			NotBefore: jwt.NewNumericDate(now),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(j.secretKey)
}

// GenerateTokenPair 生成令牌对
func (j *JWTManager) GenerateTokenPair(userID uint, username, userType string, merchantID uint) (*TokenPair, error) {
	accessToken, err := j.GenerateToken(userID, username, userType, merchantID)
	if err != nil {
		return nil, err
	}

	refreshToken, err := j.GenerateRefreshToken(userID, username, userType, merchantID)
	if err != nil {
		return nil, err
	}

	return &TokenPair{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    int64(j.expire.Seconds()),
	}, nil
}

// ParseToken 解析令牌
func (j *JWTManager) ParseToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("unexpected signing method")
		}
		return j.secretKey, nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("invalid token")
}

// ValidateToken 验证令牌
func (j *JWTManager) ValidateToken(tokenString string) (*Claims, error) {
	claims, err := j.ParseToken(tokenString)
	if err != nil {
		return nil, err
	}

	// 检查令牌是否过期
	if claims.ExpiresAt.Time.Before(time.Now()) {
		return nil, errors.New("token expired")
	}

	return claims, nil
}

// RefreshToken 刷新令牌
func (j *JWTManager) RefreshToken(refreshTokenString string) (*TokenPair, error) {
	claims, err := j.ParseToken(refreshTokenString)
	if err != nil {
		return nil, err
	}

	// 检查刷新令牌是否过期
	if claims.ExpiresAt.Time.Before(time.Now()) {
		return nil, errors.New("refresh token expired")
	}

	// 生成新的令牌对
	return j.GenerateTokenPair(claims.UserID, claims.Username, claims.UserType, claims.MerchantID)
}

// GetUserIDFromToken 从令牌中获取用户ID
func (j *JWTManager) GetUserIDFromToken(tokenString string) (uint, error) {
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		return 0, err
	}
	return claims.UserID, nil
}

// GetUserTypeFromToken 从令牌中获取用户类型
func (j *JWTManager) GetUserTypeFromToken(tokenString string) (string, error) {
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		return "", err
	}
	return claims.UserType, nil
}

// GetMerchantIDFromToken 从令牌中获取商户ID
func (j *JWTManager) GetMerchantIDFromToken(tokenString string) (uint, error) {
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		return 0, err
	}
	return claims.MerchantID, nil
}

// IsTokenExpired 检查令牌是否过期
func (j *JWTManager) IsTokenExpired(tokenString string) bool {
	claims, err := j.ParseToken(tokenString)
	if err != nil {
		return true
	}
	return claims.ExpiresAt.Time.Before(time.Now())
}

// GetTokenExpireTime 获取令牌过期时间
func (j *JWTManager) GetTokenExpireTime(tokenString string) (time.Time, error) {
	claims, err := j.ParseToken(tokenString)
	if err != nil {
		return time.Time{}, err
	}
	return claims.ExpiresAt.Time, nil
}

// GetTokenRemainingTime 获取令牌剩余时间
func (j *JWTManager) GetTokenRemainingTime(tokenString string) (time.Duration, error) {
	expireTime, err := j.GetTokenExpireTime(tokenString)
	if err != nil {
		return 0, err
	}
	return time.Until(expireTime), nil
}

// BlacklistToken 将令牌加入黑名单（需要配合Redis实现）
func (j *JWTManager) BlacklistToken(tokenString string) error {
	// 这里需要配合Redis实现令牌黑名单
	// 可以将令牌的JTI（JWT ID）存储到Redis中，设置过期时间为令牌的剩余有效期
	// 在验证令牌时检查是否在黑名单中
	return nil
}

// IsTokenBlacklisted 检查令牌是否在黑名单中
func (j *JWTManager) IsTokenBlacklisted(tokenString string) bool {
	// 这里需要配合Redis实现令牌黑名单检查
	return false
}

// UserInfo 用户信息结构
type UserInfo struct {
	UserID     uint   `json:"user_id"`
	Username   string `json:"username"`
	UserType   string `json:"user_type"`
	MerchantID uint   `json:"merchant_id,omitempty"`
}

// GetUserInfoFromToken 从令牌中获取用户信息
func (j *JWTManager) GetUserInfoFromToken(tokenString string) (*UserInfo, error) {
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		return nil, err
	}

	return &UserInfo{
		UserID:     claims.UserID,
		Username:   claims.Username,
		UserType:   claims.UserType,
		MerchantID: claims.MerchantID,
	}, nil
}

// 全局JWT管理器实例
var globalJWTManager *JWTManager

// SetGlobalJWTManager 设置全局JWT管理器
func SetGlobalJWTManager(manager *JWTManager) {
	globalJWTManager = manager
}

// GetGlobalJWTManager 获取全局JWT管理器
func GetGlobalJWTManager() *JWTManager {
	return globalJWTManager
}

// 便捷函数
func GenerateToken(userID uint, username, userType string, merchantID uint) (string, error) {
	return globalJWTManager.GenerateToken(userID, username, userType, merchantID)
}

func GenerateTokenPair(userID uint, username, userType string, merchantID uint) (*TokenPair, error) {
	return globalJWTManager.GenerateTokenPair(userID, username, userType, merchantID)
}

func ValidateToken(tokenString string) (*Claims, error) {
	return globalJWTManager.ValidateToken(tokenString)
}

func RefreshToken(refreshTokenString string) (*TokenPair, error) {
	return globalJWTManager.RefreshToken(refreshTokenString)
}

func GetUserInfoFromToken(tokenString string) (*UserInfo, error) {
	return globalJWTManager.GetUserInfoFromToken(tokenString)
}
