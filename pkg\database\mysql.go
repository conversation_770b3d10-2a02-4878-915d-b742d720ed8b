package database

import (
	"fmt"
	"log"
	"os"
	"time"

	"crmeb-go/internal/config"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

// DB 全局数据库实例
var DB *gorm.DB

// InitMySQL 初始化MySQL连接
func InitMySQL(cfg *config.DatabaseConfig) (*gorm.DB, error) {
	// 构建DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=%t&loc=%s&timeout=30s&readTimeout=30s&writeTimeout=30s",
		cfg.Username,
		cfg.Password,
		cfg.Host,
		cfg.Port,
		cfg.Database,
		cfg.Charset,
		cfg.ParseTime,
		cfg.Loc,
	)

	// 配置日志级别
	var logLevel logger.LogLevel
	switch cfg.LogLevel {
	case "silent":
		logLevel = logger.Silent
	case "error":
		logLevel = logger.Error
	case "warn":
		logLevel = logger.Warn
	case "info":
		logLevel = logger.Info
	default:
		logLevel = logger.Info
	}

	// 自定义日志配置
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags), // io writer
		logger.Config{
			SlowThreshold:             time.Second, // 慢SQL阈值
			LogLevel:                  logLevel,    // 日志级别
			IgnoreRecordNotFoundError: true,        // 忽略ErrRecordNotFound错误
			Colorful:                  false,       // 禁用彩色打印
		},
	)

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: newLogger,
		NamingStrategy: &CustomNamingStrategy{},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// 获取底层sql.DB
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(cfg.ConnMaxLifetime)

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	// 设置全局DB实例
	DB = db

	return db, nil
}

// CustomNamingStrategy 自定义命名策略
type CustomNamingStrategy struct{}

func (ns CustomNamingStrategy) TableName(str string) string {
	// 保持原有的表名命名规则，与PHP项目保持一致
	return str
}

func (ns CustomNamingStrategy) SchemaName(table string) string {
	return table
}

func (ns CustomNamingStrategy) ColumnName(table, column string) string {
	return column
}

func (ns CustomNamingStrategy) JoinTableName(str string) string {
	return str
}

func (ns CustomNamingStrategy) RelationshipFKName(relationship schema.Relationship) string {
	return relationship.Name + "_id"
}

func (ns CustomNamingStrategy) CheckerName(table, column string) string {
	return table + "_" + column + "_checker"
}

func (ns CustomNamingStrategy) IndexName(table, column string) string {
	return "idx_" + table + "_" + column
}

// GetDB 获取数据库实例
func GetDB() *gorm.DB {
	return DB
}

// Transaction 执行事务
func Transaction(fn func(tx *gorm.DB) error) error {
	return DB.Transaction(fn)
}

// Close 关闭数据库连接
func Close() error {
	if DB != nil {
		sqlDB, err := DB.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}

// Migrate 执行数据库迁移
func Migrate(models ...interface{}) error {
	return DB.AutoMigrate(models...)
}

// IsRecordNotFoundError 判断是否为记录不存在错误
func IsRecordNotFoundError(err error) bool {
	return err == gorm.ErrRecordNotFound
}

// IsDuplicateEntryError 判断是否为重复记录错误
func IsDuplicateEntryError(err error) bool {
	if err == nil {
		return false
	}
	return fmt.Sprintf("%v", err) == "Error 1062: Duplicate entry"
}

// Paginate 分页查询
func Paginate(page, pageSize int) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if page <= 0 {
			page = 1
		}

		switch {
		case pageSize > 100:
			pageSize = 100
		case pageSize <= 0:
			pageSize = 10
		}

		offset := (page - 1) * pageSize
		return db.Offset(offset).Limit(pageSize)
	}
}

// PaginateResult 分页结果
type PaginateResult struct {
	Total       int64       `json:"total"`
	Page        int         `json:"page"`
	PageSize    int         `json:"page_size"`
	TotalPages  int         `json:"total_pages"`
	HasNext     bool        `json:"has_next"`
	HasPrevious bool        `json:"has_previous"`
	Data        interface{} `json:"data"`
}

// PaginateQuery 分页查询并返回结果
func PaginateQuery(db *gorm.DB, page, pageSize int, result interface{}) (*PaginateResult, error) {
	var total int64
	
	// 计算总数
	if err := db.Count(&total).Error; err != nil {
		return nil, err
	}

	// 分页查询
	if err := db.Scopes(Paginate(page, pageSize)).Find(result).Error; err != nil {
		return nil, err
	}

	// 计算分页信息
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))
	hasNext := page < totalPages
	hasPrevious := page > 1

	return &PaginateResult{
		Total:       total,
		Page:        page,
		PageSize:    pageSize,
		TotalPages:  totalPages,
		HasNext:     hasNext,
		HasPrevious: hasPrevious,
		Data:        result,
	}, nil
}
