package logger

import (
	"io"
	"os"
	"path/filepath"
	"time"

	"crmeb-go/internal/config"
	"github.com/sirupsen/logrus"
	"gopkg.in/natefinch/lumberjack.v2"
)

// Logger 全局日志实例
var Logger *logrus.Logger

// InitLogger 初始化日志系统
func InitLogger(cfg *config.LogConfig) (*logrus.Logger, error) {
	logger := logrus.New()

	// 设置日志级别
	level, err := logrus.ParseLevel(cfg.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)

	// 设置日志格式
	if cfg.Format == "json" {
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: "2006-01-02 15:04:05",
		})
	} else {
		logger.SetFormatter(&logrus.TextFormatter{
			TimestampFormat: "2006-01-02 15:04:05",
			FullTimestamp:   true,
		})
	}

	// 设置输出
	var writers []io.Writer

	switch cfg.Output {
	case "console":
		writers = append(writers, os.Stdout)
	case "file":
		fileWriter := getFileWriter(cfg)
		writers = append(writers, fileWriter)
	case "both":
		writers = append(writers, os.Stdout)
		fileWriter := getFileWriter(cfg)
		writers = append(writers, fileWriter)
	default:
		writers = append(writers, os.Stdout)
	}

	if len(writers) > 1 {
		logger.SetOutput(io.MultiWriter(writers...))
	} else {
		logger.SetOutput(writers[0])
	}

	// 设置全局日志实例
	Logger = logger

	return logger, nil
}

// getFileWriter 获取文件写入器
func getFileWriter(cfg *config.LogConfig) io.Writer {
	// 确保日志目录存在
	logDir := filepath.Dir(cfg.FilePath)
	if err := os.MkdirAll(logDir, 0755); err != nil {
		logrus.Errorf("Failed to create log directory: %v", err)
	}

	return &lumberjack.Logger{
		Filename:   cfg.FilePath,
		MaxSize:    cfg.MaxSize,    // MB
		MaxAge:     cfg.MaxAge,     // days
		MaxBackups: cfg.MaxBackups, // files
		Compress:   cfg.Compress,   // compress rotated files
		LocalTime:  true,           // use local time
	}
}

// GetLogger 获取日志实例
func GetLogger() *logrus.Logger {
	return Logger
}

// Debug 调试日志
func Debug(args ...interface{}) {
	Logger.Debug(args...)
}

// Debugf 格式化调试日志
func Debugf(format string, args ...interface{}) {
	Logger.Debugf(format, args...)
}

// Info 信息日志
func Info(args ...interface{}) {
	Logger.Info(args...)
}

// Infof 格式化信息日志
func Infof(format string, args ...interface{}) {
	Logger.Infof(format, args...)
}

// Warn 警告日志
func Warn(args ...interface{}) {
	Logger.Warn(args...)
}

// Warnf 格式化警告日志
func Warnf(format string, args ...interface{}) {
	Logger.Warnf(format, args...)
}

// Error 错误日志
func Error(args ...interface{}) {
	Logger.Error(args...)
}

// Errorf 格式化错误日志
func Errorf(format string, args ...interface{}) {
	Logger.Errorf(format, args...)
}

// Fatal 致命错误日志
func Fatal(args ...interface{}) {
	Logger.Fatal(args...)
}

// Fatalf 格式化致命错误日志
func Fatalf(format string, args ...interface{}) {
	Logger.Fatalf(format, args...)
}

// WithField 添加字段
func WithField(key string, value interface{}) *logrus.Entry {
	return Logger.WithField(key, value)
}

// WithFields 添加多个字段
func WithFields(fields logrus.Fields) *logrus.Entry {
	return Logger.WithFields(fields)
}

// WithError 添加错误字段
func WithError(err error) *logrus.Entry {
	return Logger.WithError(err)
}

// RequestLogger 请求日志记录器
type RequestLogger struct {
	*logrus.Entry
}

// NewRequestLogger 创建请求日志记录器
func NewRequestLogger(requestID, userID, method, path string) *RequestLogger {
	entry := Logger.WithFields(logrus.Fields{
		"request_id": requestID,
		"user_id":    userID,
		"method":     method,
		"path":       path,
		"timestamp":  time.Now().Format("2006-01-02 15:04:05"),
	})
	return &RequestLogger{Entry: entry}
}

// LogRequest 记录请求日志
func (rl *RequestLogger) LogRequest(statusCode int, duration time.Duration, clientIP string) {
	rl.WithFields(logrus.Fields{
		"status_code": statusCode,
		"duration":    duration.String(),
		"client_ip":   clientIP,
	}).Info("Request completed")
}

// LogError 记录错误日志
func (rl *RequestLogger) LogError(err error, message string) {
	rl.WithError(err).Error(message)
}

// BusinessLogger 业务日志记录器
type BusinessLogger struct {
	*logrus.Entry
}

// NewBusinessLogger 创建业务日志记录器
func NewBusinessLogger(module, action string) *BusinessLogger {
	entry := Logger.WithFields(logrus.Fields{
		"module":    module,
		"action":    action,
		"timestamp": time.Now().Format("2006-01-02 15:04:05"),
	})
	return &BusinessLogger{Entry: entry}
}

// LogSuccess 记录成功日志
func (bl *BusinessLogger) LogSuccess(message string, data interface{}) {
	bl.WithField("data", data).Info(message)
}

// LogFailure 记录失败日志
func (bl *BusinessLogger) LogFailure(err error, message string) {
	bl.WithError(err).Error(message)
}

// SecurityLogger 安全日志记录器
type SecurityLogger struct {
	*logrus.Entry
}

// NewSecurityLogger 创建安全日志记录器
func NewSecurityLogger(event, userID, clientIP string) *SecurityLogger {
	entry := Logger.WithFields(logrus.Fields{
		"event":     event,
		"user_id":   userID,
		"client_ip": clientIP,
		"timestamp": time.Now().Format("2006-01-02 15:04:05"),
	})
	return &SecurityLogger{Entry: entry}
}

// LogSecurityEvent 记录安全事件
func (sl *SecurityLogger) LogSecurityEvent(level, message string, details interface{}) {
	entry := sl.WithFields(logrus.Fields{
		"level":   level,
		"details": details,
	})

	switch level {
	case "critical":
		entry.Error(message)
	case "warning":
		entry.Warn(message)
	default:
		entry.Info(message)
	}
}

// PerformanceLogger 性能日志记录器
type PerformanceLogger struct {
	*logrus.Entry
}

// NewPerformanceLogger 创建性能日志记录器
func NewPerformanceLogger(operation string) *PerformanceLogger {
	entry := Logger.WithFields(logrus.Fields{
		"operation": operation,
		"timestamp": time.Now().Format("2006-01-02 15:04:05"),
	})
	return &PerformanceLogger{Entry: entry}
}

// LogPerformance 记录性能日志
func (pl *PerformanceLogger) LogPerformance(duration time.Duration, details interface{}) {
	pl.WithFields(logrus.Fields{
		"duration": duration.String(),
		"details":  details,
	}).Info("Performance metrics")
}

// LogSlowQuery 记录慢查询
func (pl *PerformanceLogger) LogSlowQuery(query string, duration time.Duration, args interface{}) {
	pl.WithFields(logrus.Fields{
		"query":    query,
		"duration": duration.String(),
		"args":     args,
	}).Warn("Slow query detected")
}
