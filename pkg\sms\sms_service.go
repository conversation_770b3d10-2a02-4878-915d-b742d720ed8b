package sms

import (
	"context"
	"crypto/rand"
	"errors"
	"fmt"
	"math/big"
	"time"

	"crmeb-go/pkg/cache"
)

// SmsService 短信服务接口
type SmsService interface {
	// SendCode 发送验证码
	SendCode(ctx context.Context, phone, codeType string) error
	// VerifyCode 验证验证码
	VerifyCode(ctx context.Context, phone, code, codeType string) error
	// GenerateCode 生成验证码
	GenerateCode() string
}

// smsService 短信服务实现
type smsService struct {
	cache cache.Cache
}

// NewSmsService 创建短信服务实例
func NewSmsService(cache cache.Cache) SmsService {
	return &smsService{
		cache: cache,
	}
}

// SendCode 发送验证码
func (s *smsService) SendCode(ctx context.Context, phone, codeType string) error {
	// 检查发送频率限制
	limitKey := fmt.Sprintf("sms_limit_%s", phone)
	limit, _ := s.cache.Get(ctx, limitKey)
	if limit != nil {
		if limitCount, ok := limit.(int); ok && limitCount >= 5 {
			return errors.New("请求太频繁请稍后再试")
		}
	}

	// 生成验证码
	code := s.GenerateCode()
	
	// 存储验证码到缓存，有效期5分钟
	codeKey := s.getSmsKey(phone, codeType)
	err := s.cache.Set(ctx, codeKey, code, 5*time.Minute)
	if err != nil {
		return fmt.Errorf("存储验证码失败: %v", err)
	}

	// 更新发送频率限制
	currentLimit := 0
	if limit != nil {
		if limitCount, ok := limit.(int); ok {
			currentLimit = limitCount
		}
	}
	s.cache.Set(ctx, limitKey, currentLimit+1, time.Minute)

	// TODO: 这里应该调用实际的短信发送服务
	// 目前为了开发方便，直接打印验证码
	fmt.Printf("发送短信验证码到 %s: %s (类型: %s)\n", phone, code, codeType)
	
	return nil
}

// VerifyCode 验证验证码
func (s *smsService) VerifyCode(ctx context.Context, phone, code, codeType string) error {
	codeKey := s.getSmsKey(phone, codeType)
	
	// 从缓存中获取验证码
	cachedCode, err := s.cache.Get(ctx, codeKey)
	if err != nil {
		return errors.New("验证码已过期")
	}
	
	if cachedCode == nil {
		return errors.New("验证码不存在或已过期")
	}
	
	// 验证码比较
	if cachedCodeStr, ok := cachedCode.(string); !ok || cachedCodeStr != code {
		return errors.New("验证码不正确")
	}
	
	// 验证成功后删除验证码
	s.cache.Delete(ctx, codeKey)
	
	return nil
}

// GenerateCode 生成4位数字验证码
func (s *smsService) GenerateCode() string {
	// 生成1000-9999之间的随机数
	min := int64(1000)
	max := int64(9999)
	
	n, err := rand.Int(rand.Reader, big.NewInt(max-min+1))
	if err != nil {
		// 如果随机数生成失败，使用时间戳生成
		return fmt.Sprintf("%04d", time.Now().Unix()%10000)
	}
	
	return fmt.Sprintf("%04d", n.Int64()+min)
}

// getSmsKey 获取短信验证码的缓存键
func (s *smsService) getSmsKey(phone, codeType string) string {
	return fmt.Sprintf("sms_code_%s_%s", phone, codeType)
}

// 全局短信服务实例
var globalSmsService SmsService

// SetGlobalSmsService 设置全局短信服务
func SetGlobalSmsService(service SmsService) {
	globalSmsService = service
}

// GetGlobalSmsService 获取全局短信服务
func GetGlobalSmsService() SmsService {
	return globalSmsService
}

// 便捷函数
func SendCode(ctx context.Context, phone, codeType string) error {
	return globalSmsService.SendCode(ctx, phone, codeType)
}

func VerifyCode(ctx context.Context, phone, code, codeType string) error {
	return globalSmsService.VerifyCode(ctx, phone, code, codeType)
}
