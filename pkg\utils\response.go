package utils

import (
	"context"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// Response 统一响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"msg"`
	Data    interface{} `json:"data,omitempty"`
	Time    int64       `json:"time"`
}

// PageResponse 分页响应结构
type PageResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"msg"`
	Data    interface{} `json:"data"`
	Total   int64       `json:"total"`
	Page    int         `json:"page"`
	Size    int         `json:"size"`
	Time    int64       `json:"time"`
}

// 响应状态码常量
const (
	// 成功
	CodeSuccess = 200

	// 客户端错误
	CodeBadRequest          = 400
	CodeUnauthorized        = 401
	CodeForbidden           = 403
	CodeNotFound            = 404
	CodeMethodNotAllowed    = 405
	CodeRequestTimeout      = 408
	CodeTooManyRequests     = 429

	// 服务器错误
	CodeInternalServerError = 500
	CodeBadGateway          = 502
	CodeServiceUnavailable  = 503
	CodeGatewayTimeout      = 504

	// 业务错误码
	CodeValidationError     = 1001
	CodeDatabaseError       = 1002
	CodeCacheError          = 1003
	CodeExternalServiceError = 1004
	CodeBusinessLogicError  = 1005
)

// 错误消息常量
const (
	MsgSuccess              = "操作成功"
	MsgBadRequest           = "请求参数错误"
	MsgUnauthorized         = "未授权访问"
	MsgForbidden            = "禁止访问"
	MsgNotFound             = "资源不存在"
	MsgMethodNotAllowed     = "请求方法不允许"
	MsgRequestTimeout       = "请求超时"
	MsgTooManyRequests      = "请求过于频繁"
	MsgInternalServerError  = "服务器内部错误"
	MsgBadGateway           = "网关错误"
	MsgServiceUnavailable   = "服务不可用"
	MsgGatewayTimeout       = "网关超时"
	MsgValidationError      = "数据验证失败"
	MsgDatabaseError        = "数据库操作失败"
	MsgCacheError           = "缓存操作失败"
	MsgExternalServiceError = "外部服务调用失败"
	MsgBusinessLogicError   = "业务逻辑错误"
)

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    CodeSuccess,
		Message: MsgSuccess,
		Data:    data,
		Time:    time.Now().Unix(),
	})
}

// SuccessWithMessage 带消息的成功响应
func SuccessWithMessage(c *gin.Context, message string, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    CodeSuccess,
		Message: message,
		Data:    data,
		Time:    time.Now().Unix(),
	})
}

// SuccessPage 分页成功响应
func SuccessPage(c *gin.Context, data interface{}, total int64, page, size int) {
	c.JSON(http.StatusOK, PageResponse{
		Code:    CodeSuccess,
		Message: MsgSuccess,
		Data:    data,
		Total:   total,
		Page:    page,
		Size:    size,
		Time:    time.Now().Unix(),
	})
}

// Error 错误响应
func Error(c *gin.Context, code int, message string) {
	httpStatus := getHTTPStatus(code)
	c.JSON(httpStatus, Response{
		Code:    code,
		Message: message,
		Time:    time.Now().Unix(),
	})
}

// ErrorWithData 带数据的错误响应
func ErrorWithData(c *gin.Context, code int, message string, data interface{}) {
	httpStatus := getHTTPStatus(code)
	c.JSON(httpStatus, Response{
		Code:    code,
		Message: message,
		Data:    data,
		Time:    time.Now().Unix(),
	})
}

// BadRequest 400错误响应
func BadRequest(c *gin.Context, message string) {
	Error(c, CodeBadRequest, message)
}

// Unauthorized 401错误响应
func Unauthorized(c *gin.Context, message string) {
	Error(c, CodeUnauthorized, message)
}

// Forbidden 403错误响应
func Forbidden(c *gin.Context, message string) {
	Error(c, CodeForbidden, message)
}

// NotFound 404错误响应
func NotFound(c *gin.Context, message string) {
	Error(c, CodeNotFound, message)
}

// MethodNotAllowed 405错误响应
func MethodNotAllowed(c *gin.Context, message string) {
	Error(c, CodeMethodNotAllowed, message)
}

// RequestTimeout 408错误响应
func RequestTimeout(c *gin.Context, message string) {
	Error(c, CodeRequestTimeout, message)
}

// TooManyRequests 429错误响应
func TooManyRequests(c *gin.Context, message string) {
	Error(c, CodeTooManyRequests, message)
}

// InternalServerError 500错误响应
func InternalServerError(c *gin.Context, message string) {
	Error(c, CodeInternalServerError, message)
}

// BadGateway 502错误响应
func BadGateway(c *gin.Context, message string) {
	Error(c, CodeBadGateway, message)
}

// ServiceUnavailable 503错误响应
func ServiceUnavailable(c *gin.Context, message string) {
	Error(c, CodeServiceUnavailable, message)
}

// GatewayTimeout 504错误响应
func GatewayTimeout(c *gin.Context, message string) {
	Error(c, CodeGatewayTimeout, message)
}

// ValidationError 验证错误响应
func ValidationError(c *gin.Context, message string) {
	Error(c, CodeValidationError, message)
}

// DatabaseError 数据库错误响应
func DatabaseError(c *gin.Context, message string) {
	Error(c, CodeDatabaseError, message)
}

// CacheError 缓存错误响应
func CacheError(c *gin.Context, message string) {
	Error(c, CodeCacheError, message)
}

// ExternalServiceError 外部服务错误响应
func ExternalServiceError(c *gin.Context, message string) {
	Error(c, CodeExternalServiceError, message)
}

// BusinessLogicError 业务逻辑错误响应
func BusinessLogicError(c *gin.Context, message string) {
	Error(c, CodeBusinessLogicError, message)
}

// getHTTPStatus 根据业务错误码获取HTTP状态码
func getHTTPStatus(code int) int {
	switch {
	case code >= 200 && code < 300:
		return http.StatusOK
	case code >= 400 && code < 500:
		return code
	case code >= 500 && code < 600:
		return code
	case code >= 1000 && code < 2000:
		return http.StatusBadRequest
	default:
		return http.StatusInternalServerError
	}
}

// ResponseWriter 响应写入器接口
type ResponseWriter interface {
	Success(data interface{})
	SuccessWithMessage(message string, data interface{})
	SuccessPage(data interface{}, total int64, page, size int)
	Error(code int, message string)
	ErrorWithData(code int, message string, data interface{})
	BadRequest(message string)
	Unauthorized(message string)
	Forbidden(message string)
	NotFound(message string)
	InternalServerError(message string)
	ValidationError(message string)
	DatabaseError(message string)
	BusinessLogicError(message string)
}

// GinResponseWriter Gin响应写入器实现
type GinResponseWriter struct {
	ctx *gin.Context
}

// NewResponseWriter 创建响应写入器
func NewResponseWriter(c *gin.Context) ResponseWriter {
	return &GinResponseWriter{ctx: c}
}

// Success 成功响应
func (w *GinResponseWriter) Success(data interface{}) {
	Success(w.ctx, data)
}

// SuccessWithMessage 带消息的成功响应
func (w *GinResponseWriter) SuccessWithMessage(message string, data interface{}) {
	SuccessWithMessage(w.ctx, message, data)
}

// SuccessPage 分页成功响应
func (w *GinResponseWriter) SuccessPage(data interface{}, total int64, page, size int) {
	SuccessPage(w.ctx, data, total, page, size)
}

// Error 错误响应
func (w *GinResponseWriter) Error(code int, message string) {
	Error(w.ctx, code, message)
}

// ErrorWithData 带数据的错误响应
func (w *GinResponseWriter) ErrorWithData(code int, message string, data interface{}) {
	ErrorWithData(w.ctx, code, message, data)
}

// BadRequest 400错误响应
func (w *GinResponseWriter) BadRequest(message string) {
	BadRequest(w.ctx, message)
}

// Unauthorized 401错误响应
func (w *GinResponseWriter) Unauthorized(message string) {
	Unauthorized(w.ctx, message)
}

// Forbidden 403错误响应
func (w *GinResponseWriter) Forbidden(message string) {
	Forbidden(w.ctx, message)
}

// NotFound 404错误响应
func (w *GinResponseWriter) NotFound(message string) {
	NotFound(w.ctx, message)
}

// InternalServerError 500错误响应
func (w *GinResponseWriter) InternalServerError(message string) {
	InternalServerError(w.ctx, message)
}

// ValidationError 验证错误响应
func (w *GinResponseWriter) ValidationError(message string) {
	ValidationError(w.ctx, message)
}

// DatabaseError 数据库错误响应
func (w *GinResponseWriter) DatabaseError(message string) {
	DatabaseError(w.ctx, message)
}

// BusinessLogicError 业务逻辑错误响应
func (w *GinResponseWriter) BusinessLogicError(message string) {
	BusinessLogicError(w.ctx, message)
}

// GetClientIP 获取客户端IP地址
func GetClientIP(ctx context.Context) string {
	if ginCtx, ok := ctx.(*gin.Context); ok {
		return ginCtx.ClientIP()
	}
	return "unknown"
}
