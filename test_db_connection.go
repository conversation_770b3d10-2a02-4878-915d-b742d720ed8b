package main

import (
	"database/sql"
	"fmt"
	"log"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 数据库配置
	host := "************"
	port := 3306
	username := "root"
	password := "OnZ8M5H6Zk"
	database := "crmeb"

	// 构建DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=true&loc=Local&timeout=30s&readTimeout=30s&writeTimeout=30s",
		username,
		password,
		host,
		port,
		database,
	)

	fmt.Println("正在测试数据库连接...")
	fmt.Printf("主机: %s:%d\n", host, port)
	fmt.Printf("用户: %s\n", username)
	fmt.Printf("数据库: %s\n", database)
	fmt.Println("DSN:", dsn)
	fmt.Println()

	// 尝试连接数据库
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("打开数据库连接失败: %v", err)
	}
	defer db.Close()

	// 设置连接池参数
	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(time.Hour)

	// 测试连接
	fmt.Println("正在ping数据库...")
	err = db.Ping()
	if err != nil {
		log.Fatalf("数据库ping失败: %v", err)
	}

	fmt.Println("✅ 数据库连接成功!")

	// 测试简单查询
	fmt.Println("正在测试查询...")
	var version string
	err = db.QueryRow("SELECT VERSION()").Scan(&version)
	if err != nil {
		log.Printf("查询版本失败: %v", err)
	} else {
		fmt.Printf("✅ MySQL版本: %s\n", version)
	}

	// 测试数据库列表
	fmt.Println("正在测试数据库访问...")
	rows, err := db.Query("SHOW DATABASES")
	if err != nil {
		log.Printf("查询数据库列表失败: %v", err)
	} else {
		fmt.Println("✅ 可访问的数据库:")
		for rows.Next() {
			var dbName string
			if err := rows.Scan(&dbName); err == nil {
				fmt.Printf("  - %s\n", dbName)
			}
		}
		rows.Close()
	}

	fmt.Println("\n数据库连接测试完成!")
}
