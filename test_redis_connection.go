package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/redis/go-redis/v9"
)

func main() {
	// Redis配置
	host := "************"
	port := 6379
	password := "suxingye799"
	db := 0

	fmt.Println("正在测试Redis连接...")
	fmt.Printf("主机: %s:%d\n", host, port)
	fmt.Printf("数据库: %d\n", db)
	fmt.Printf("密码: %s\n", password)
	fmt.Println()

	// 创建Redis客户端
	client := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", host, port),
		Password: password,
		DB:       db,
	})
	defer client.Close()

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	fmt.Println("正在ping Redis...")
	result, err := client.Ping(ctx).Result()
	if err != nil {
		log.Fatalf("Redis ping失败: %v", err)
	}

	fmt.Printf("✅ Redis连接成功! 响应: %s\n", result)

	// 测试基本操作
	fmt.Println("正在测试基本操作...")

	// 设置值
	err = client.Set(ctx, "test_key", "test_value", time.Minute).Err()
	if err != nil {
		log.Printf("设置值失败: %v", err)
	} else {
		fmt.Println("✅ 设置值成功")
	}

	// 获取值
	val, err := client.Get(ctx, "test_key").Result()
	if err != nil {
		log.Printf("获取值失败: %v", err)
	} else {
		fmt.Printf("✅ 获取值成功: %s\n", val)
	}

	// 删除值
	err = client.Del(ctx, "test_key").Err()
	if err != nil {
		log.Printf("删除值失败: %v", err)
	} else {
		fmt.Println("✅ 删除值成功")
	}

	fmt.Println("\nRedis连接测试完成!")
}
