package unit

import (
	"context"
	"testing"
	"time"

	"crmeb-go/internal/model"
	userRepo "crmeb-go/internal/repository/user"
	userService "crmeb-go/internal/service/user"
	"crmeb-go/pkg/auth"
	"crmeb-go/pkg/cache"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockUserRepository 模拟用户仓储
type MockUserRepository struct {
	mock.Mock
}

func (m *MockUserRepository) Create(ctx context.Context, user *model.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserRepository) GetByID(ctx context.Context, uid uint) (*model.User, error) {
	args := m.Called(ctx, uid)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockUserRepository) GetByAccount(ctx context.Context, account string) (*model.User, error) {
	args := m.Called(ctx, account)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockUserRepository) GetByPhone(ctx context.Context, phone string) (*model.User, error) {
	args := m.Called(ctx, phone)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockUserRepository) Update(ctx context.Context, user *model.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserRepository) Delete(ctx context.Context, uid uint) error {
	args := m.Called(ctx, uid)
	return args.Error(0)
}

func (m *MockUserRepository) List(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*model.User, int64, error) {
	args := m.Called(ctx, page, pageSize, filters)
	return args.Get(0).([]*model.User), args.Get(1).(int64), args.Error(2)
}

func (m *MockUserRepository) Exists(ctx context.Context, field, value string) (bool, error) {
	args := m.Called(ctx, field, value)
	return args.Bool(0), args.Error(1)
}

func (m *MockUserRepository) Count(ctx context.Context, filters map[string]interface{}) (int64, error) {
	args := m.Called(ctx, filters)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockUserRepository) UpdateLastLogin(ctx context.Context, uid uint, ip string) error {
	args := m.Called(ctx, uid, ip)
	return args.Error(0)
}

func (m *MockUserRepository) UpdatePassword(ctx context.Context, uid uint, password string) error {
	args := m.Called(ctx, uid, password)
	return args.Error(0)
}

func (m *MockUserRepository) UpdateStatus(ctx context.Context, uid uint, status int8) error {
	args := m.Called(ctx, uid, status)
	return args.Error(0)
}

func (m *MockUserRepository) UpdateMoney(ctx context.Context, uid uint, amount float64) error {
	args := m.Called(ctx, uid, amount)
	return args.Error(0)
}

func (m *MockUserRepository) UpdateIntegral(ctx context.Context, uid uint, integral int) error {
	args := m.Called(ctx, uid, integral)
	return args.Error(0)
}

func (m *MockUserRepository) GetUserStats(ctx context.Context, uid uint) (*userRepo.UserStats, error) {
	args := m.Called(ctx, uid)
	return args.Get(0).(*userRepo.UserStats), args.Error(1)
}

func (m *MockUserRepository) GetTotalUsers(ctx context.Context) (int64, error) {
	args := m.Called(ctx)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockUserRepository) GetActiveUsers(ctx context.Context, days int) (int64, error) {
	args := m.Called(ctx, days)
	return args.Get(0).(int64), args.Error(1)
}

// MockUserBillRepository 模拟用户账单仓储
type MockUserBillRepository struct {
	mock.Mock
}

func (m *MockUserBillRepository) Create(ctx context.Context, bill *model.UserBill) error {
	args := m.Called(ctx, bill)
	return args.Error(0)
}

func (m *MockUserBillRepository) GetByUserID(ctx context.Context, uid uint, page, pageSize int) ([]*model.UserBill, int64, error) {
	args := m.Called(ctx, uid, page, pageSize)
	return args.Get(0).([]*model.UserBill), args.Get(1).(int64), args.Error(2)
}

func (m *MockUserBillRepository) GetByType(ctx context.Context, uid uint, billType string, page, pageSize int) ([]*model.UserBill, int64, error) {
	args := m.Called(ctx, uid, billType, page, pageSize)
	return args.Get(0).([]*model.UserBill), args.Get(1).(int64), args.Error(2)
}

func (m *MockUserBillRepository) GetUserBalance(ctx context.Context, uid uint, category string) (float64, error) {
	args := m.Called(ctx, uid, category)
	return args.Get(0).(float64), args.Error(1)
}

// MockCache 模拟缓存
type MockCache struct {
	mock.Mock
}

func (m *MockCache) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	args := m.Called(ctx, key, value, expiration)
	return args.Error(0)
}

func (m *MockCache) Get(ctx context.Context, key string) (string, error) {
	args := m.Called(ctx, key)
	return args.String(0), args.Error(1)
}

func (m *MockCache) GetObject(ctx context.Context, key string, dest interface{}) error {
	args := m.Called(ctx, key, dest)
	return args.Error(0)
}

func (m *MockCache) SetObject(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	args := m.Called(ctx, key, value, expiration)
	return args.Error(0)
}

func (m *MockCache) Delete(ctx context.Context, keys ...string) error {
	args := m.Called(ctx, keys)
	return args.Error(0)
}

func (m *MockCache) Exists(ctx context.Context, key string) (bool, error) {
	args := m.Called(ctx, key)
	return args.Bool(0), args.Error(1)
}

// 实现其他接口方法...
func (m *MockCache) Expire(ctx context.Context, key string, expiration time.Duration) error { return nil }
func (m *MockCache) TTL(ctx context.Context, key string) (time.Duration, error) { return 0, nil }
func (m *MockCache) Increment(ctx context.Context, key string) (int64, error) { return 0, nil }
func (m *MockCache) Decrement(ctx context.Context, key string) (int64, error) { return 0, nil }
func (m *MockCache) IncrementBy(ctx context.Context, key string, value int64) (int64, error) { return 0, nil }
func (m *MockCache) DecrementBy(ctx context.Context, key string, value int64) (int64, error) { return 0, nil }
func (m *MockCache) HSet(ctx context.Context, key string, values ...interface{}) error { return nil }
func (m *MockCache) HGet(ctx context.Context, key, field string) (string, error) { return "", nil }
func (m *MockCache) HGetAll(ctx context.Context, key string) (map[string]string, error) { return nil, nil }
func (m *MockCache) HDel(ctx context.Context, key string, fields ...string) error { return nil }
func (m *MockCache) SAdd(ctx context.Context, key string, members ...interface{}) error { return nil }
func (m *MockCache) SMembers(ctx context.Context, key string) ([]string, error) { return nil, nil }
func (m *MockCache) SRem(ctx context.Context, key string, members ...interface{}) error { return nil }
func (m *MockCache) ZAdd(ctx context.Context, key string, members ...interface{}) error { return nil }
func (m *MockCache) ZRange(ctx context.Context, key string, start, stop int64) ([]string, error) { return nil, nil }
func (m *MockCache) ZRem(ctx context.Context, key string, members ...interface{}) error { return nil }
func (m *MockCache) Lock(ctx context.Context, key string, expiration time.Duration) (bool, error) { return false, nil }
func (m *MockCache) Unlock(ctx context.Context, key string) error { return nil }

// TestUserService_Login 测试用户登录
func TestUserService_Login(t *testing.T) {
	// 创建模拟对象
	mockUserRepo := new(MockUserRepository)
	mockUserBillRepo := new(MockUserBillRepository)
	mockCache := new(MockCache)
	
	// 创建JWT管理器
	jwtManager := &auth.JWTManager{}
	
	// 创建用户服务
	userSvc := userService.NewUserService(mockUserRepo, mockUserBillRepo, mockCache, jwtManager)
	
	// 测试数据
	ctx := context.Background()
	loginReq := &userService.LoginRequest{
		Account:  "testuser",
		Password: "password123",
		LoginIP:  "127.0.0.1",
	}
	
	// 模拟用户数据
	user := &model.User{
		UID:      1,
		Account:  "testuser",
		Pwd:      "$2a$10$hashedpassword", // 假设这是加密后的密码
		Nickname: "Test User",
		Avatar:   "avatar.jpg",
		Status:   1,
	}
	
	// 设置期望
	mockUserRepo.On("GetByAccount", ctx, "testuser").Return(user, nil)
	mockUserRepo.On("UpdateLastLogin", ctx, uint(1), "127.0.0.1").Return(nil)
	
	// 执行测试
	resp, err := userSvc.Login(ctx, loginReq)
	
	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, uint(1), resp.UID)
	assert.Equal(t, "testuser", resp.Account)
	assert.Equal(t, "Test User", resp.Nickname)
	assert.NotNil(t, resp.Token)
	
	// 验证模拟对象的调用
	mockUserRepo.AssertExpectations(t)
}

// TestUserService_Register 测试用户注册
func TestUserService_Register(t *testing.T) {
	// 创建模拟对象
	mockUserRepo := new(MockUserRepository)
	mockUserBillRepo := new(MockUserBillRepository)
	mockCache := new(MockCache)
	
	// 创建JWT管理器
	jwtManager := &auth.JWTManager{}
	
	// 创建用户服务
	userSvc := userService.NewUserService(mockUserRepo, mockUserBillRepo, mockCache, jwtManager)
	
	// 测试数据
	ctx := context.Background()
	registerReq := &userService.RegisterRequest{
		Account:  "newuser",
		Password: "password123",
		Phone:    "***********",
		Nickname: "New User",
		Code:     "123456",
	}
	
	// 设置期望
	mockCache.On("Get", ctx, "sms_code:***********").Return("123456", nil)
	mockUserRepo.On("Exists", ctx, "account", "newuser").Return(false, nil)
	mockUserRepo.On("Exists", ctx, "phone", "***********").Return(false, nil)
	mockUserRepo.On("Create", ctx, mock.AnythingOfType("*model.User")).Return(nil)
	mockCache.On("Delete", ctx, []string{"sms_code:***********"}).Return(nil)
	
	// 执行测试
	resp, err := userSvc.Register(ctx, registerReq)
	
	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, "newuser", resp.Account)
	assert.Equal(t, "New User", resp.Nickname)
	assert.NotNil(t, resp.Token)
	
	// 验证模拟对象的调用
	mockUserRepo.AssertExpectations(t)
	mockCache.AssertExpectations(t)
}
